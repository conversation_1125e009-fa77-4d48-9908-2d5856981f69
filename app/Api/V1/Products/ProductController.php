<?php declare(strict_types = 1);

namespace App\Api\V1\Products;

use Apitte\Core\Annotation\Controller\Method;
use Apitte\Core\Annotation\Controller\Path;
use Apitte\Core\Annotation\Controller\RequestParameter;
use Apitte\Core\Annotation\Controller\Response;
use Apitte\Core\Annotation\Controller\Tag;
use Apitte\Core\Exception\Api\ClientErrorException;
use Apitte\Core\Http\ApiRequest;
use Apitte\Core\Http\ApiResponse;
use Apitte\Core\Schema\EndpointParameter;
use App\Api\Decorator\MutationFinder;
use App\Api\V1\Controllers\BaseV1Controller;
use App\Api\V1\Pages\Detail\Response\Page;
use App\Model\Link\LinkFactory;
use App\Model\Orm\ProductVariant\ProductVariantRepository;

#[Path('/products')]
final class ProductController extends BaseV1Controller
{

	public function __construct(
		private readonly ProductVariantRepository $productVariantRepository,
		private readonly LinkFactory $linkFactory,
	)
	{
	}

	#[Tag('noAuthentication')]
	#[Path('/{mutationId}/ean/{ean}')]
	#[Method('GET')]
	#[RequestParameter(name: 'ean', type: 'string', in: EndpointParameter::IN_PATH, required: true, description: 'Product variant\'s EAN')]
	#[RequestParameter(name: MutationFinder::MUTATION_ID_PARAMETER_NAME, type: 'int', in: EndpointParameter::IN_PATH, required: true, description: 'Mutation Id')]
	#[Response(description: 'Success', code: '200', entity: Page::class)]
	#[Response(description: 'Not found', code: '404')]
	final public function getByEan(ApiRequest $request, ApiResponse $response): ApiResponse
	{
		$productVariant = $this->productVariantRepository->getBy([
			'ean' => $request->getParameter('ean'),
		]);

		if ($productVariant === null) {
			throw new ClientErrorException('Not found', ApiResponse::S404_NOT_FOUND);
		}

		$productLocalization = $productVariant->product->productLocalizations->toCollection()->getBy(['mutation' => $request->getParameter('mutationId')]);

		if ($productLocalization === null) {
			throw new ClientErrorException('Not found', ApiResponse::S404_NOT_FOUND);
		}

		if (!$productLocalization->public) {
			throw new ClientErrorException('Not published', ApiResponse::S401_UNAUTHORIZED);
		}

		return $this->jsonResponse(
			[
				'link' => $this->linkFactory->linkTranslateToNette($productLocalization, ['mutation' => $productLocalization->mutation]),
			],
			$response,
		);
	}

}
