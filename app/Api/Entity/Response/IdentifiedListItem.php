<?php declare(strict_types = 1);

namespace App\Api\Entity\Response;

use Apitte\Core\Mapping\Response\BasicEntity;
use App\Model\Orm\BaseEntity;
use App\Model\Orm\RoutableEntity;
use App\PostType\Blog\Model\Orm\BlogLocalization;

class IdentifiedListItem extends BasicEntity
{

	public readonly int $id;

	public function __construct(BaseEntity|RoutableEntity $entity)
	{
		$this->id = $entity->id;
	}

}
