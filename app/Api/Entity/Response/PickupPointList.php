<?php declare(strict_types = 1);

namespace App\Api\Entity\Response;

use Apitte\Core\Mapping\Response\BasicEntity;
use Nextras\Dbal\Result\Row;

class PickupPointList extends BasicEntity
{

	/** @var PickupPointListItem[] */
	public readonly array $pickupPoints;

	public function __construct(
		array $pickupPoints
	)
	{
		$items = [];
		foreach ($pickupPoints as $item) {
			assert($item instanceof Row);
			$items[] = new PickupPointListItem($item);
		}

		$this->pickupPoints = $items;
	}

}
