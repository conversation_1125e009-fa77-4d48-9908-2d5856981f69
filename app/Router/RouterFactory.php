<?php declare(strict_types = 1);

namespace App\Router;

use App\Model\Cache\RouterCache;
use App\Model\Router\Filter;
use App\Model\Router\FilterRedirect;
use App\Model\Router\FilterReview;
use App\Model\Router\FilterSharedLibrary;
use Nette\Application\Routers\RouteList;
use Nette\Http\Request;
use Nette\Routing\Route;
use Nette\Routing\Router;
use function sprintf;

final class RouterFactory
{

	public function __construct(
		private readonly Filter $filter,
		private readonly FilterRedirect $filterRedirect,
		private readonly FilterReview $filterReview,
		private readonly FilterSharedLibrary $filterSharedLibrary,

		private readonly RouterCache $routerCache,
		private readonly Request $request,
	)
	{
	}

	/**
	 * @param array<string, string> $postTypeRoutes
	 */
	public function createRouter(
		string $adminAlias,
		array $postTypeRoutes,
	): RouteList
	{
		$router = new RouteList();
		$router->addRoute('index.php', 'Front:Homepage:default', Router::ONE_WAY);
		$router->addRoute('/data/images-<sizeName>/<bucket .+>/<filename>.<extension ([a-zA-Z0-9])+>', 'Front:Image:resample', Router::ONE_WAY);
		$router->addRoute('/data/images/<bucket .+>/<filename>.<extension svg|gif>', 'Front:Image:resample', Router::ONE_WAY);


		foreach ($postTypeRoutes as $postType => $path) {
			$router->addRoute($adminAlias . '/' . $path . '/<action>', ['module' => $postType . ':Admin', 'presenter' => $postType, 'action' => 'default']);
		}
		$router->add($adminRouter = new RouteList('Admin'));

		if (str_contains($this->request->getUrl()->getPath(), $adminAlias)) {
			$adminRouter->addRoute($adminAlias . '/ztracene-heslo', 'Sign:lostPassword');
			$adminRouter->addRoute($adminAlias . '/reset-hesla', 'Sign:resetPassword');
		}
		$adminRouter->addRoute($adminAlias . '/<presenter>/<action>', 'Homepage:default');

		$router->addRoute('/robots.txt', 'Front:Robots:default', Router::ONE_WAY);


		$urlPrefixes = $this->routerCache->findUrlPrefixes();
		$prefixMask = $urlPrefixes !== '' ? $urlPrefixes : '[a-z]{2}';

		$myLibraryAliases = $this->routerCache->findMyLibraryAliases();
		if ($myLibraryAliases !== '') {
			$router->addRoute(sprintf('[<urlPrefix %s>/]<alias %s>/<sharedLibraryUid>', $prefixMask, $myLibraryAliases),  [
				null => [
					Route::FilterIn => $this->filterSharedLibrary->in(...),
					Route::FilterOut => $this->filterSharedLibrary->out(...),
				],
			]);
		}

		$aliasesForReview = $this->routerCache->findAliasesForReview();
		if ($aliasesForReview !== '') {
			$router->addRoute(sprintf('[<urlPrefix %s>/]<tmp %s>/<alias .*>', $prefixMask, $aliasesForReview), [
				null => [
					Route::FilterIn => $this->filterReview->in(...),
					Route::FilterOut => $this->filterReview->out(...),
				],
			]);
		}

		$frontFilters = [
			null => [
				Route::FilterIn => $this->filter->in(...),
				Route::FilterOut => $this->filter->out(...),
			],
		];

		$router->addRoute(sprintf('[<urlPrefix %s>/]<alias .*>', $prefixMask), $frontFilters);
		$router->addRoute(sprintf('[<urlPrefix %s>]', $prefixMask), $frontFilters);


		$router->addRoute(sprintf('[<urlPrefix %s>/]<alias .*>', $prefixMask), [
			null => [
				Route::FilterIn => $this->filterRedirect->in(...),
			],
		], Router::ONE_WAY);


		return $router;
	}
}
