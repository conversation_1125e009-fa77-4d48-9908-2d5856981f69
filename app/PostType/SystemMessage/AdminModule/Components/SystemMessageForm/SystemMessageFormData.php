<?php declare(strict_types = 1);

namespace App\PostType\SystemMessage\AdminModule\Components\SystemMessageForm;

use App\AdminModule\Presenters\Mutation\Components\Form\FormData\SetupFormData;
use Nette\Utils\ArrayHash;

final class SystemMessageFormData
{

	public bool $isPublic;

	public bool $isUpperPosition;

	public bool $closable;

	public ?string $position = null;

	public string $internalName;

	public string $description;

	public ArrayHash $validity;

	public ArrayHash $publish;

	public int $editedBy;

	public SetupFormData $setup;

}
