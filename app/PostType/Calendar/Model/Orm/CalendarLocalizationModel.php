<?php declare(strict_types = 1);

namespace App\PostType\Calendar\Model\Orm;

use Jaybizzle\CrawlerDetect\CrawlerDetect;

class CalendarLocalizationModel
{

	public function __construct(
		private readonly CalendarLocalizationRepository $calendarLocalizationRepository,
		private readonly CalendarLocalizationTreeRepository $calendarLocalizationTreeRepository,
	)
	{
	}


	public function increaseViews(CalendarLocalization $calendarLocalization): bool
	{
		$crawlerDetect = new CrawlerDetect();
		if ($crawlerDetect->isCrawler()) {
			return false;
		}

		$calendarLocalization->viewsNumber++;
		$this->calendarLocalizationRepository->persistAndFlush($calendarLocalization);
		return true;
	}

	/**
	 * @param array<int, int> $treeIds
	 */
	public function setCategoriesByIds(CalendarLocalization $calendarLocalization, array $treeIds): void
	{
		$currentCalendarLocalizationTrees = $this->calendarLocalizationTreeRepository->findBy(['calendarLocalization' => $calendarLocalization])->fetchPairs('tree->id');
		$sort = 0;
		foreach ($treeIds as $treeId) {

			if (isset($currentCalendarLocalizationTrees[$treeId])) {
				// update sort
				$calendarLocalizationTree = $currentCalendarLocalizationTrees[$treeId];
				assert($calendarLocalizationTree instanceof CalendarLocalizationTree);
				$calendarLocalizationTree->sort = $sort;
				// unset array
				unset($currentCalendarLocalizationTrees[$treeId]);
			} else {
				// create new
				$calendarLocalizationTree = new CalendarLocalizationTree();
				$calendarLocalizationTree->calendarLocalization = $calendarLocalization;
				$calendarLocalizationTree->tree = $treeId;
				$calendarLocalizationTree->sort = $sort;
			}
			$sort++;
		}

		// remove old
		if ($currentCalendarLocalizationTrees !== []) {
			foreach ($currentCalendarLocalizationTrees as $currentCalendarLocalizationTree) {
				$this->calendarLocalizationTreeRepository->remove($currentCalendarLocalizationTree);
			}
		}
	}

}
