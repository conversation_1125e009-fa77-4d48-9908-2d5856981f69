<?php

declare(strict_types=1);

namespace App\PostType\Page\Model\Orm;

use App\Model\BucketFilter\CatalogParameter;
use App\Model\Link\VirtualLink;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\Traits\HasTranslator;
use App\Model\Orm\TreeTree\TreeTree;
use Nextras\Orm\Collection\ICollection;

/**
 * @property int|null $hasLinkedCategories {default 0}
 *
 *
 * RELATIONS
 *
 *
 * VIRTUALS
 * @property-read Parameter[]|array $importantParameters {virtual}
 * @property-read bool $hasCourses {virtual}
 *
 * @property-read ICollection<Tree> $linkedCategories {virtual}
 * @property-read ICollection<Tree> $linkedCategoriesAll {virtual}
 * @property ICollection<Tree> $parentLinkedCategories {virtual}
 * @property ICollection<Tree> $parentLinkedCategoriesAll {virtual}
 */
final class CatalogTree extends Tree
{

	use HasTranslator;

	private CatalogParameter $catalogParameter;

	public function injectCatalogParameter(CatalogParameter $catalogParameter): void
	{
		$this->catalogParameter = $catalogParameter;
	}


	/**
	 * @return ICollection<Tree>
	 */
	protected function getterLinkedCategories(): ICollection
	{
		return $this->linkedCategoriesAll->findBy($this->treeRepository->getPublicOnlyWhere());
	}

	/**
	 * @return ICollection<Tree>
	 */
	public function getterLinkedCategoriesAll(): ICollection
	{
		return $this->treeRepository->findAttachedTreesInRelations($this, TreeTree::TYPE_LINKED_CATEGORY);
	}

	/**
	 * @return ICollection<Tree>
	 */
	public function getterParentLinkedCategories(): ICollection
	{
		return $this->parentLinkedCategoriesAll->findBy($this->treeRepository->getPublicOnlyWhere());
	}

	/**
	 * @return ICollection<Tree>
	 */
	public function getterParentLinkedCategoriesAll(): ICollection
	{
		return $this->treeRepository->findMainTreesInRelations($this, TreeTree::TYPE_LINKED_CATEGORY);
	}


	protected function getterImportantParameters(): array
	{
		if (!isset($this->cache['importantParameters'])) {
			$this->cache['importantParameters'] = $this->catalogParameter->getPossibleIndexableParametersForCatalog($this)->fetchPairs('uid', null);
		}

		return $this->cache['importantParameters'];
	}


	public function isImportantParameter(string $name): bool
	{
		return isset($this->importantParameters[$name]);
	}


	public function getParentIdByLevel(int $level = 1): int
	{
		if (isset($this->path[$level])) {
			return $this->path[$level];
		}
		return $this->id;
	}

	public function getParentByLevel(int $level = 1): ?Tree
	{
		$parentId = $this->getParentIdByLevel($level);
		return $this->treeRepository->getById($parentId);
	}

	public function getterHasCourses(): bool
	{
		if ($this->uid === 'courses') {
			return true;
		}

		foreach ($this->pathItems as $pathItem) {
			if ($pathItem->uid === 'courses') {
				return true;
			}
		}
		return false;
	}

	public function getVirtualCategorySystemLink(): ?Tree
	{
		return $this->getVirtualLink()?->page;
	}

	public function getVirtualCategoryCustomLink(): ?string
	{
		return $this->getVirtualLink()?->href;
	}

	public function isVirtualCategory(): bool
	{
		return $this->getVirtualLink() !== null;
	}

	public function getVirtualLink(): ?VirtualLink
	{
		return VirtualLink::fromTreeEntity($this);
	}

	/**
	 * Searches for a custom field value in the current category and its parent categories.
	 * Traverses the path of categories and returns the first non-null value found for the specified custom field path.
	 *
	 * @param string $cfPath Path to the custom field using '->' notation (e.g., 'field->subfield')
	 * @param bool $startFromEnd If true, starts searching from the end of the catalog path
	 * @return mixed The first non-null value found, or null if no value is found
	 */
	public function getFirstCfValueInCategoryPath(string $cfPath, bool $startFromEnd = false): mixed
	{
		$pathItems = array_merge([$this], $this->pathItems);

		if ($startFromEnd) {
			$pathItems = array_reverse($pathItems);
		}
		$cfPathArr = explode('->', $cfPath);
		foreach ($pathItems as $category) {
			$cf = $category->cf;
			foreach ($cfPathArr as $pathItem) {
				if (isset($cf->$pathItem)) {
					$cf = $cf->$pathItem;
				} else {
					$cf = null;
					continue 2;
				}
			}
			return $cf;
		}
		return null;
	}

}
