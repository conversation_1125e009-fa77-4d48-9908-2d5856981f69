<?php

namespace App\PostType\Blog\FrontModule\Components\BlogList;

use App\FrontModule\Components\CatalogProducts\CatalogProductsData;
use App\Model\BucketFilter\BucketFilterBuilder;
use Closure;

class BlogListCallbackBuilder
{

	/**
	 * @phpstan-return Closure(int $limit, int $offset): BlogListData
	 */
	public function build(BucketFilterBuilder $dataSource): Closure
	{
		//if ($dataSource instanceof BucketFilterBuilder) {
			return function (int $limit, int $offset) use ($dataSource) {
				$itemsObject = $dataSource->getItems($limit, $offset);
				return new BlogListData(
					$itemsObject->items, /** @phpstan-ignore-line */
					$itemsObject->totalCount,
				);
			};
		//}

		//throw new LogicException(sprintf('Missing datasource for %s.', $dataSource::class));
	}

}
