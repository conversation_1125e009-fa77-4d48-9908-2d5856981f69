<?php declare(strict_types = 1);

namespace App\PostType\UserAnimal\Model\Orm;

use App\Model\Orm\Mutation\Mutation;
use Nextras\Orm\Entity\Entity;
use App\Model\Orm\JsonContainer;		// phpcs:ignore

// phpcs:ignore

/**
 * @property int $id {primary}
 * @property string $name
 *
 * RELATIONS
 * @property Mutation $mutation {m:1 Mutation, oneSided=true}
 * @property UserAnimalType $userAnimalType {m:1 UserAnimalType::$localizations}
 */
class UserAnimalTypeLocalization extends Entity
{

	public function getId(): int
	{
		return $this->id;
	}

}
