<?php declare(strict_types = 1);

namespace App\PostType\UserAnimal\Model\Orm;

use App\Model\Orm\Mutation\Mutation;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method ICollection<UserAnimalTypeLocalization> getUserAnimalTypes(Mutation $mutation)
 * @extends Repository<UserAnimalTypeLocalization>
 */
class UserAnimalTypeLocalizationRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [UserAnimalTypeLocalization::class];
	}

}
