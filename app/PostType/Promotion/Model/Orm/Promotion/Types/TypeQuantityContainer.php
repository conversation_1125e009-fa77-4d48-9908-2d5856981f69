<?php declare(strict_types = 1);

namespace App\PostType\Promotion\Model\Orm\Promotion\Types;

use Nette\Utils\Json;
use Nextras\Orm\Entity\ImmutableValuePropertyWrapper;

final class TypeQuantityContainer extends ImmutableValuePropertyWrapper
{

	public function convertToRawValue($value): string
	{
		assert($value instanceof TypeQuantity);
		return Json::encode($value);
	}

	public function convertFromRawValue($value): TypeQuantity
	{
		$value = TypeRawDataHelper::sanitizeValue($value);
		$jsonData = Json::decode($value);

		$items = [];
		if (isset($jsonData->items)) {
			$items = $jsonData->items;
		}

		return new TypeQuantity(
			$items
		);
	}

}
