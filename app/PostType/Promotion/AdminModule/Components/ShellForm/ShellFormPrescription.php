<?php declare(strict_types=1);

namespace App\PostType\Promotion\AdminModule\Components\ShellForm;

use App\PostType\Core\AdminModule\Components\ShellForm\Definition\Extenders\CustomFormExtender;
use App\PostType\Core\AdminModule\Components\ShellForm\Definition\Extenders\Templates\CommonTemplatePart;
use App\PostType\Core\AdminModule\Components\ShellForm\Definition\FormDefinition;
use App\PostType\Promotion\Model\Orm\Promotion\Promotion;
use App\PostType\Promotion\Model\Orm\Promotion\PromotionLocalization;
use App\PostType\Promotion\Model\Orm\Promotion\Types\PromotionDiscountType;
use App\PostType\Promotion\Model\Orm\Promotion\Types\PromotionType;
use Nette\Application\UI\Form;

class ShellFormPrescription
{

	public function __construct()
	{
	}

	public function getPrescription(): FormDefinition
	{
		$extenders = [];
		$extenders[] = $this->addType();

		return new FormDefinition(
			form: $this->createForm(),
			extenders: $extenders,
		);
	}

	private function createForm(): Form
	{
		$form = new Form();
		$form->setMappedType(PromotionShellFormData::class);
		return $form;
	}

	private function addType(): CustomFormExtender
	{
		return new CustomFormExtender(
			addHandler: function (Form $form) {
			//				$typeValues = array_map(fn(PromotionType $item) => $item->value, PromotionType::cases());
			//				$types = array_combine($typeValues, $typeValues);

				$types = Promotion::getTypeSelectData();
				$form->addSelect('type', 'type', $types)->setRequired();
			},
			successHandler: function (Form $form, PromotionShellFormData $formData, PromotionLocalization $promotionLocalization) {
				$promotionLocalization->getParent()->type = PromotionType::from($formData->type);
				$promotionLocalization->getParent()->discountType = PromotionDiscountType::FixedPrice;
			},
			templateParts: [
				new CommonTemplatePart(
					templateFile: __DIR__ . '/templates/type.latte',
					type: CommonTemplatePart::TYPE_MAIN,
				),
			],
		);
	}

}
