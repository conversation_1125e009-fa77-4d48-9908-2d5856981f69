<?php declare(strict_types=1);

namespace App\PostType\Promotion\AdminModule\Components\PromotionsDataGrid;

use App\Model\Translator;
use App\PostType\Core\AdminModule\Components\DataGrid\Definition\DataGridDefinition;
use App\PostType\Core\AdminModule\Components\DataGrid\Definition\Extenders\CustomDataGridExtender;
use App\PostType\Promotion\Model\Orm\Promotion\Promotion;
use App\PostType\Promotion\Model\Orm\Promotion\PromotionLocalization;
use Nextras\Orm\Collection\DbalCollection;
use Ublaboo\DataGrid\DataGrid;

class PromotionDataGridPrescription
{

	public function __construct(
		private readonly Translator $translator,
	)
	{
	}


	public function getPrescription(): DataGridDefinition
	{
		$grid = new DataGrid();

		$grid->addColumnText('type', 'type', 'promotion.type')->setRenderer(
			fn(PromotionLocalization $promotionLocalization) => $this->translator->translate($promotionLocalization->getParent()->type->value)
		)->setFilterSelect(
			array_map(fn(string $name) => $this->translator->translate($name), Promotion::getTypeSelectData())
		)->setCondition(function (DbalCollection $collection, string $value) {
			$collection->getQueryBuilder()
				->joinLeft('[promotion] AS [promotion]', '[promotion.id] = [promotion_localization.promotionId]')
				->andWhere('promotion.type = %s', $value);
		});

		$sortColumns = new CustomDataGridExtender(
			function (DataGrid $grid) {
				$grid->setColumnsOrder(['name', 'internalName', 'type', 'mutation']);
			}
		);

		return new DataGridDefinition(
			dataGrid: $grid,
			beforeRenderExtenders: [$sortColumns],
		);
	}

}
