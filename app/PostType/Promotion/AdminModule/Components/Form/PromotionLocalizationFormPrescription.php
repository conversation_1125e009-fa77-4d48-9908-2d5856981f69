<?php declare(strict_types=1);

namespace App\PostType\Promotion\AdminModule\Components\Form;

use App\Model\Translator;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\CustomFormExtender;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates\CommonTemplatePart;
use App\PostType\Core\AdminModule\Components\Form\Definition\FormDefinition;
use App\PostType\Promotion\AdminModule\Components\Form\FormData\PromotionLocalizationFormData;
use App\PostType\Promotion\Model\Orm\Promotion\PromotionLocalization;
use App\PostType\Promotion\Model\Orm\Promotion\Types\PromotionType;
use App\PostType\Promotion\Model\Orm\Promotion\Types\TypeBonusItem;
use App\PostType\Promotion\Model\Orm\Promotion\Types\TypeQuantity;
use Nette\Application\UI\Form;

class PromotionLocalizationFormPrescription
{

	public function __construct(
		private readonly Translator $translator,
	)
	{
	}

	public function getPrescription(PromotionLocalization $promotionLocalization, array $rawPostData): FormDefinition
	{
		$form = new Form();
		$form->setMappedType(PromotionLocalizationFormData::class);

		$extenders = [];

		$extenders[] = new CustomFormExtender(
			addHandler: function (Form $form) {
			},
			successHandler: function (Form $form, PromotionLocalizationFormData $data) {
			},
			templateParts: [
				new CommonTemplatePart(
					templateFile: __DIR__ . '/templates/side.latte',
					type: CommonTemplatePart::TYPE_SIDE,
					parameters: [
						'promotionLocalization' => $promotionLocalization,
					]
				),
			]
		);

		match ($promotionLocalization->getParent()->type) {
//			PromotionType::typePresent => null,
			PromotionType::typeQuantity => $extenders[] = $this->addQualityType($form, $promotionLocalization, $rawPostData),
			PromotionType::typeBonusItem => $extenders[] = $this->addBonusItemType($form, $promotionLocalization),
		};

		return new FormDefinition(
			form: $form,
			extenders: $extenders,
			templateParameters: [
				'title' => $promotionLocalization->getParent()->internalName . ' - ' . $this->translator->translate($promotionLocalization->getParent()->type->value),
			]
		);
	}


	private function addBonusItemType(Form $form, PromotionLocalization $promotionLocalization): CustomFormExtender
	{
		$templateParts = [];
		$templateParts[] = new CommonTemplatePart(
			templateFile: __DIR__ . '/templates/typeBonusItem.latte',
			type: CommonTemplatePart::TYPE_MAIN,
			parameters: [
				'promotionLocalization' => $promotionLocalization,
			]
		);

		return new CustomFormExtender(
			addHandler: function (Form $form) use ($promotionLocalization) {
				$container = $form->addContainer('typeBonusItemFormData');
				$typeBonusItem = $promotionLocalization->getParent()->typeBonusItem;
				$container->addInteger('mainProductCount', 'main_product_count')
					->setRequired()
					->setDefaultValue($typeBonusItem->mainProductCount);
				$container->addInteger('subordinateProductCount', 'subordinate_product_count')
					->setRequired()
					->setDefaultValue($typeBonusItem->subordinateProductCount);
				$container->addInteger('discountOnSubordinate', 'subordinate_discount')
					->setRequired()
					->setDefaultValue($typeBonusItem->discountOnSubordinate);
			},
			successHandler: function (Form $form, PromotionLocalizationFormData $data) use ($promotionLocalization) {
				if ($data->typeBonusItemFormData !== null) {
					$promotionLocalization->getParent()->typeBonusItem = new TypeBonusItem(
						$data->typeBonusItemFormData->mainProductCount,
						$data->typeBonusItemFormData->subordinateProductCount,
						$data->typeBonusItemFormData->discountOnSubordinate,
					);
				}
			},
			templateParts: $templateParts
		);
	}

	private function addQualityType(Form $form, PromotionLocalization $promotionLocalization, array $rawPostData): CustomFormExtender
	{
		$templateParts = [];
		$templateParts[] = new CommonTemplatePart(
			templateFile: __DIR__ . '/templates/typeQuantity.latte',
			type: CommonTemplatePart::TYPE_MAIN,
			parameters: [
				'promotionLocalization' => $promotionLocalization,
			]
		);
		$templateParts[] = new CommonTemplatePart(
			templateFile: __DIR__ . '/templates/newItemMarkerTypeQuantity.latte',
			type: CommonTemplatePart::TYPE_RELATION_PRESCRIPTION,
			parameters: [
			//				'component' => $promotionLocalization,
			]
		);

		return new CustomFormExtender(
			addHandler: function (Form $form) use ($promotionLocalization, $rawPostData) {
				$relationName = 'typeQualityFormData';
				$container = $form->addContainer($relationName);

				if (isset($rawPostData[$relationName]) && $containerPostData = $rawPostData[$relationName]) {
					foreach ($containerPostData as $itemKey => $postData) {
						$itemContainer = $container->addContainer($itemKey);
						$itemContainer->addInteger('price', 'price')->setDefaultValue($postData['price']);
						$itemContainer->addInteger('amount', 'amount')->setDefaultValue($postData['amount']);
					}
				} else {
					$itemsObject = $promotionLocalization->getParent()->typeQuantity;
					foreach ($itemsObject->items as $itemKey => $item) {
						$itemContainer = $container->addContainer($itemKey);
						$itemContainer->addInteger('price', 'price')->setDefaultValue($item->price);
						$itemContainer->addInteger('amount', 'amount')->setDefaultValue($item->amount);
					}
				}
				//add suggest blueprint
				$itemContainer = $container->addContainer('newItemMarker');
				$itemContainer->addInteger('price', 'price');
				$itemContainer->addInteger('amount', 'amount');
			},
			successHandler: function (Form $form, PromotionLocalizationFormData $data) use ($promotionLocalization) {
				if ($data->typeQualityFormData !== null) {
					unset($data->typeQualityFormData['newItemMarker']);
					usort($data->typeQualityFormData, function (array $itemA, array $itemB) {
						return $itemB['amount'] <=> $itemA['amount'];
					});
					$promotionLocalization->getParent()->typeQuantity = new TypeQuantity(
						array_map(fn ($item) => (object) $item, $data->typeQualityFormData)
					);
				}
			},
			templateParts: $templateParts
		);
	}

}
