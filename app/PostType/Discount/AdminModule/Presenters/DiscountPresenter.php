<?php declare(strict_types = 1);

namespace App\PostType\Discount\AdminModule\Presenters;

use App\AdminModule\Components\ItemSearch\FoundItem;
use App\AdminModule\Components\ItemSearch\ItemSearch;
use App\AdminModule\Components\ItemSearch\ItemSearchFactory;
use App\AdminModule\Presenters\BasePresenter;
use App\Model\ElasticSearch\All\Repository;
use App\Model\Orm\EsIndex\EsIndexRepository;
use App\Model\Orm\Product\Product;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGrid;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGridFactory;
use App\PostType\Core\AdminModule\Components\Form\Form;
use App\PostType\Core\AdminModule\Components\Form\FormFactory;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellForm;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellFormFactory;
use App\PostType\Discount\AdminModule\Components\DiscountDataGrid\DiscountDataGridPrescription;
use App\PostType\Discount\AdminModule\Components\DiscountDetailForm\DiscountDetailFormPrescription;
use App\PostType\Discount\AdminModule\Components\ProductsList\ProductsList;
use App\PostType\Discount\AdminModule\Components\ProductsList\ProductsListFactory;
use App\PostType\Discount\Model\Orm\DiscountLocalization\DiscountLocalization;
use App\PostType\Discount\Model\DiscountLocalizationFacade;
use Nette\Application\Attributes\Persistent;
use Nette\DI\Attributes\Inject;
use Nextras\Dbal\Drivers\Exception\UniqueConstraintViolationException;
use Nextras\Orm\Collection\ICollection;

class DiscountPresenter extends BasePresenter
{

	private const ORM_REPOSITORY_NAME = 'discount';

	#[Persistent]
	public int $id;

	#[Inject]
	public ProductsListFactory $productsListFactory;

	#[Inject]
	public DiscountDataGridPrescription $discountDataGridPrescription;

	private DiscountLocalization $discountLocalization;

	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
		private readonly FormFactory $formFactory,
		private readonly ShellFormFactory $shellFormFactory,
		private readonly DiscountLocalizationFacade $discountLocalizationFacade,
		private readonly DiscountDetailFormPrescription $discountDetailFormDefinition,
		private readonly ItemSearchFactory $itemSearchFactory,
		private readonly Repository $esAllRepository,
		private readonly \App\Model\ElasticSearch\Product\Facade $esProductFacade,
		private readonly EsIndexRepository $esIndexRepository,
	)
	{
		parent::__construct();
	}

	public function renderDefault(): void
	{
	}

	public function actionCreate(): void
	{
		$this->discountLocalizationFacade->create($this->mutationHolder->getMutation(), null);

		$this->terminate();
	}

	public function actionEdit(int $id): void
	{
		/**
		 * @var DiscountLocalization|null $discountLocalization
		 */
		$discountLocalization = $this->orm->discountLocalization->getById($id);
		if ($discountLocalization === null) {
			$this->redirect('default');
		}

		$this->discountLocalization = $discountLocalization;
	}

	public function renderEdit(int $id): void
	{
	}

	public function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create(entity: null, facade: $this->discountLocalizationFacade);
	}

	protected function createComponentGrid(): DataGrid
	{
		$collection = $this->orm->discountLocalization->findBy([])->orderBy(['discount->order' => ICollection::ASC_NULLS_LAST]);

		return $this->dataGridFactory->create(self::ORM_REPOSITORY_NAME, $collection, dataGridDefinition: $this->discountDataGridPrescription->get());
	}

	protected function createComponentDiscountForm(): Form
	{
		return $this->formFactory->create($this->discountLocalizationFacade, $this->discountLocalization, $this->userEntity, $this->discountDetailFormDefinition->getPrescription($this->discountLocalization));
	}

	public function actionProducts(int $id): void
	{
		$this->loadLocalization($id);

		$this->id = $id;
	}

	private function loadLocalization(int $id): void
	{
		/**
		 * @var DiscountLocalization|null $discountLocalization
		 */
		$discountLocalization = $this->orm->discountLocalization->getById($id);
		if ($discountLocalization === null) {
			$this->redirect('default');
		}

		$this->discountLocalization = $discountLocalization;
	}

	public function renderProducts(int $id): void
	{
		$this->template->add('sectionTitle', $this->translator->translate('discountProduct'));
		$this->template->add('discountLocalization', $this->discountLocalization);
	}

	public function createComponentProductSearch(): ItemSearch
	{
		$discountLocalization = $this->discountLocalization;
		$clickItemFunction = function ($foundItemId) use ($discountLocalization): void {
			try {
				$discountLocalization->getParent()->products->add($foundItemId);
				$this->orm->persistAndFlush($discountLocalization->getParent());

				if ($product = $discountLocalization->getParent()->products->toCollection()->getById($foundItemId)) {
					$this->esProductFacade->updateAllMutations($product);
				}

			} catch (UniqueConstraintViolationException) {
				// ignore duplicity
			}

			$this->redrawControl();
		};

		$searchItemFunction = function (string $searchString): array {
			$esIndex = $this->esIndexRepository->getAllLastActive($this->orm->mutation->getRsDefault());
			if ($esIndex !== null) {
				/** @var array<Product> $products */
				$products = $this->esAllRepository->searchProduct($esIndex, $searchString)->fetchAll();
				return array_map(fn(Product $product) => new FoundItem($product->id, $product->internalName), $products);
			}
			return [];
		};

		return $this->itemSearchFactory->create($searchItemFunction, $clickItemFunction);
	}

	public function createComponentProductsList(): ProductsList
	{
		return $this->productsListFactory->create($this->discountLocalization);
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->getAction() . '.latte');
	}

}
