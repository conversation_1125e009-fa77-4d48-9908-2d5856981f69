cf:
	# fields:

	templates:
		blogTagLocalization:
			settings:
				type: group
				label: "Nastavení"
				items:
					color:
						type: select
						label: "Barva š<PERSON>ku"
						defaultValue: "dark"
						options: [
							{ label: "<PERSON><PERSON><PERSON><PERSON> (defaultní)", value: 'dark' },
							{ label: "Světle šedá", value: "gray-light" },
							{ label: "Světle fialová", value: "purple-light" },
							{ label: "Světle zelená", value: "green-light" },
							{ label: "Světle modrá", value: "blue-light" },
							{ label: "Světle oranžová", value: "orange-light" },
							{ label: "<PERSON>erve<PERSON>", value: "red" },
							{ label: "<PERSON><PERSON><PERSON><PERSON>", value: "white" },
							{ label: "Zelená", value: "green" },
							{ label: "Modrá", value: "blue" },
							{ label: "Žlut<PERSON>", value: "yellow-gradient" },
							{ label: "Přechod", value: "rainbow" },
						]

application:
	mapping:
		BlogTag: App\PostType\BlogTag\*Module\Presenters\*Presenter

parameters:
	postTypeRoutes:
		BlogTag: blog-tag

services:
	- App\PostType\BlogTag\Model\BlogTagLocalizationFacade
	- App\PostType\BlogTag\Model\BlogTagModel
	- App\PostType\BlogTag\AdminModule\Components\BlogTagDataGrid\BlogTagDataGridPrescription
	- App\PostType\BlogTag\AdminModule\Components\BlogTagDetailForm\BlogTagDetailFormPrescription
