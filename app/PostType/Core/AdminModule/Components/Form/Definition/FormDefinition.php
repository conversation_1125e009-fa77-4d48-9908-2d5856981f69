<?php

namespace App\PostType\Core\AdminModule\Components\Form\Definition;

use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\FormExtender;
use Nette\Application\UI\Form;

class FormDefinition
{
	/**
	 * @param FormExtender[] $extenders
	 */
	public function __construct(
		public readonly ?Form $form = null,
		public readonly array $extenders = [],
		public readonly array $templateParameters = [],
	)
	{}
}
