{if method_exists($cfObject, 'getCfScheme') && $cfObject->getCfScheme()}


	{var $icon = $templates.'/part/icons/list.svg'}

	{var $props = [
		title: $title,
		id: $containerName . '_' .$itemName,
		icon: $icon,
		variant: 'main',
		open: true,
		classes: ['u-mb-xxs'],
	]}


	{formContainer $containerName}
		{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
			{block content}
				<div class="b-std u-mb-sm">
					<h3 class="b-std__title title"></h3>
					{include './custom-fields-element.latte',
						class=>'b-std__content',
						schema=>$cfObject->getCfSchemeJson(),
						content=>$cfObject->getCfContent(),
						uploadLink=>$fileUploadLink,
						inputName=>$itemName,
						mutationId=>$mutation->id,
					}
				</div>
			{/block}
		{/embed}
	{/formContainer}
{/if}

