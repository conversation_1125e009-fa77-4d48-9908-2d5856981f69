<?php declare(strict_types = 1);

namespace App\PostType\Supplier\Model\Orm\Supplier;

use App\Model\Orm\Traits\HasCustomFields;
use App\Model\Orm\User\User;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;
use App\Model\Orm\JsonContainer;// phpcs:ignore

/**
 * @property int                             $id                      {primary}
 * @property int|null $erpId
 * @property string|null $erpCode
 * @property string                          $internalName            {default ''}
 * @property ArrayHash                       $customFieldsJson        {container JsonContainer}
 * @property DateTimeImmutable|null $editedTime       {default null}
 * @property string|null $syncChecksum {default null}
 * @property DateTimeImmutable|null $syncTime {default null}
 *
 *
 * RELATIONS
 * @property User|null              $editedBy         {m:1 User::$suppliers}
 *
 * VIRTUAL
 * @property ArrayHash|null                  $cf                      {virtual}
 */
class Supplier extends Entity
{

	use HasCustomFields;

	public function getInternalName(): string
	{
		return $this->internalName;
	}

	public function setInternalName(string $internalName): void
	{
		$this->internalName = $internalName;
	}



	public function getClosingTime(): ?array
	{
		return $this->loadCache('supplierOpeningHours', function () {
			$supplierClosingTime = (array) ($this->customFieldsJson?->delivery[0]?->closingTime[0] ?? []);

			$isNull = true;
			for ($i = 0; $i < 7; $i++) {
				if ( ! isset($supplierClosingTime[$i])) {
					$supplierClosingTime[$i] = null;
				} else {
					$isNull = false;
				}
			}

			if ($isNull) {
				return null;
			}

			ksort($supplierClosingTime);
			return $supplierClosingTime;
		});
	}

	public function getStandardDays(): ?int
	{
		return $this->loadCache('getStandardDays', function () {
			if ( ! isset($this->customFieldsJson->delivery[0]->standardDays)) {
				return null;
			}
			return (int) $this->customFieldsJson->delivery[0]->standardDays;
		});
	}

	public function getClosingAddDays(): ?int
	{
		return $this->loadCache('getClosingAddDays', function () {
			if ( ! isset($this->customFieldsJson->delivery[0]->closingAddDays)) {
				return null;
			}
			return (int) $this->customFieldsJson->delivery[0]->closingAddDays;
		});
	}

}
