<?php declare(strict_types = 1);

namespace App\PostType\Tag\Model;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\PostType\Core\Model\EntityLocalizationFacade;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\Tag\Model\Orm\Tag\HasTagRepository;
use App\PostType\Tag\Model\Orm\Tag\Tag;
use App\PostType\Tag\Model\Orm\TagLocalization\TagLocalization;

class TagLocalizationFacade implements EntityLocalizationFacade
{

	use HasTagRepository;

	public function __construct(private readonly Orm $orm)
	{
	}

	public function create(Mutation $mutation, ?ParentEntity $localizableEntity): LocalizationEntity
	{
		$localization = new TagLocalization();

		$this->orm->tagLocalization->attach($localization);

		$localization->mutation = $mutation;

		if ($localizableEntity === null) {
			$localizableEntity = new Tag();
			$localizableEntity->type = TagType::custom;
			$localization->tag = $localizableEntity;
		} else {
			assert($localizableEntity instanceof Tag);
			$localization->tag = $localizableEntity;
		}

		$this->orm->persistAndFlush($localization);

		return $localization;
	}

	public function remove(LocalizationEntity $localizableEntity): void
	{
		assert($localizableEntity instanceof TagLocalization);

		$parent = $localizableEntity->getParent();
		$this->orm->tagLocalization->remove($localizableEntity);

		if ($parent->localizations->count() === 0) {
			$this->orm->tag->remove($parent);
		}

		$this->orm->flush();
	}

}
