<?php declare(strict_types = 1);

namespace App\PostType\Tag\AdminModule\Presenters;

use App\AdminModule\Presenters\BasePresenter;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGrid;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGridFactory;
use App\PostType\Core\AdminModule\Components\Form\Form;
use App\PostType\Core\AdminModule\Components\Form\FormFactory;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellForm;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellFormFactory;
use App\PostType\Tag\AdminModule\Components\TagDataGrid\TagDataGridPrescription;
use App\PostType\Tag\AdminModule\Components\TagDetailForm\TagDetailFormPrescription;
use App\PostType\Tag\Model\Orm\TagLocalization\TagLocalization;
use App\PostType\Tag\Model\TagLocalizationFacade;
use Nette\DI\Attributes\Inject;

final class TagPresenter extends BasePresenter
{

	private const ORM_REPOSITORY_NAME = 'tag';

	#[Inject]
	public TagDataGridPrescription $tagDataGridPrescription;

	private TagLocalization $tagLocalization;

	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
		private readonly FormFactory $formFactory,
		private readonly TagLocalizationFacade $tagLocalizationFacade,
		private readonly TagDetailFormPrescription $tagDetailFormDefinition,
		private readonly ShellFormFactory $shellFormFactory,
	)
	{
		parent::__construct();
	}

	public function actionCreate(): void
	{
		$this->tagLocalizationFacade->create($this->mutationHolder->getMutation(), null);

		$this->terminate();
	}

	public function actionEdit(int $id): void
	{
		/**
		 * @var TagLocalization|null $tagLocalization
		 */
		$tagLocalization = $this->orm->tagLocalization->getById($id);
		if ($tagLocalization === null) {
			$this->redirect('default');
		}

		$this->tagLocalization = $tagLocalization;
	}

	public function renderEdit(int $id): void
	{
	}

	protected function createComponentGrid(): DataGrid
	{
		$collection = $this->orm->tagLocalization->findAll();

		return $this->dataGridFactory->create(self::ORM_REPOSITORY_NAME, $collection, dataGridDefinition: $this->tagDataGridPrescription->get());
	}

	protected function createComponentTagForm(): Form
	{
		return $this->formFactory->create($this->tagLocalizationFacade, $this->tagLocalization, $this->userEntity, $this->tagDetailFormDefinition->getPrescription($this->tagLocalization));
	}

	public function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create(entity: null, facade: $this->tagLocalizationFacade);
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->getAction() . '.latte');
	}

}
