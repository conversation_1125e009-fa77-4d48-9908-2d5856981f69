<?php declare(strict_types = 1);

namespace App\Utils\Vocative;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\String\StringRepository;
use Nette\Caching\Cache;
use Nette\Caching\Storage;
use Throwable;

/**
 * Service for converting names to vocative case in Czech and Slovak languages
 */
class VocativeService implements VocativeInterface
{

	private NameVocative $nameVocative;
    private StringRepository $stringRepository;
    private Cache $cache;

    public function __construct(
        StringRepository $stringRepository,
        Storage $storage
    ) {
        $this->nameVocative = new NameVocative();
        $this->stringRepository = $stringRepository;
        $this->cache = new Cache($storage, 'vocative');
    }

	/**
	 * Converts a name to vocative case
	 *
	 * @param string $firstName First name in nominative case
	 * @param Mutation $mutation Language mutation
	 * @param string|null $lastName Last name (optional, helps with gender detection)
	 * @param string $gender Gender (GENDER_MAN, GENDER_WOMAN, GENDER_AUTO)
	 * @return string First name in vocative case
	 * @throws Throwable
	 */
    public function vocative(string $firstName, Mutation $mutation, ?string $lastName = null, string $gender = self::GENDER_AUTO): string
    {
        // For non-Czech and non-Slovak languages, return the original name
        if (!in_array($mutation->langCode, ['cs', 'sk'])) {
            return $firstName;
        }

        // Determine gender from last name if provided and gender not explicitly specified
        if ($lastName !== null && $gender === self::GENDER_AUTO) {
            $gender = $this->nameVocative->getGender($lastName, true);
        } elseif ($gender === self::GENDER_AUTO) {
            $gender = $this->nameVocative->getGender($firstName);
        }

        // Try to load from cache
	    $cacheKey = sprintf('vocative_%s_%s_%s_%s', $firstName, $lastName ?? 'no_lastname', $mutation->langCode, $gender);
        $result = $this->getFromCache($cacheKey);
        if ($result !== null) {
            return $result;
        }

        // Try to find in custom strings database
        $result = $this->getCustomVocative($mutation, 'vocative_', $firstName, $cacheKey);
        if ($result !== null) {
            return $result;
        }

        // Use the vocative library
        $result = $this->nameVocative->vocative($firstName, $gender, false); // Always treat as first name

        // Save to cache
        $this->saveToCache($cacheKey, $result);

        return $result;
    }

	/**
	 * Converts a last name to vocative case
	 *
	 * @param string $lastName Last name in nominative case
	 * @param Mutation $mutation Language mutation
	 * @param string $gender Gender (GENDER_MAN, GENDER_WOMAN, GENDER_AUTO)
	 * @return string Last name in vocative case
	 * @throws Throwable
	 */
    public function vocativeLastName(string $lastName, Mutation $mutation, string $gender = self::GENDER_AUTO): string
    {
        // For non-Czech and non-Slovak languages, return the original name
        if (!in_array($mutation->langCode, ['cs', 'sk'])) {
            return $lastName;
        }

        // Determine gender if not explicitly specified
        if ($gender === self::GENDER_AUTO) {
            $gender = $this->nameVocative->getGender($lastName, true);
        }

        // Try to load from cache
	    $cacheKey = sprintf('vocative_lastname_%s_%s_%s', $lastName, $mutation->langCode, $gender);
        $result = $this->getFromCache($cacheKey);
        if ($result !== null) {
            return $result;
        }

        // Try to find in custom rules database
        $result = $this->getCustomVocative($mutation, 'vocative_lastname_', $lastName, $cacheKey);
        if ($result !== null) {
            return $result;
        }

        // Use the vocative library
        $result = $this->nameVocative->vocative($lastName, $gender, true); // Always treat as last name

        // Save to cache
        $this->saveToCache($cacheKey, $result);

        return $result;
    }

    /**
     * Gets value from cache
     *
     * @param string $cacheKey Cache key
     * @return string|null Value from cache or null if not found
     */
    private function getFromCache(string $cacheKey): ?string
    {
        return $this->cache->load($cacheKey);
    }

    /**
     * Saves value to cache
     *
     * @param string $cacheKey Cache key
     * @param string $value Value to save
     */
    private function saveToCache(string $cacheKey, string $value): void
    {
        $this->cache->save($cacheKey, $value);
    }

    /**
     * Gets custom rule from repository and saves it to cache if found
     *
     * @param Mutation $mutation Language mutation
     * @param string $prefix Prefix for the rule name (e.g. 'vocative_' or 'vocative_lastname_')
     * @param string $name Name to get rule for
     * @param string $cacheKey Cache key to save the result
     * @return string|null Rule value or null if not found
     */
    private function getCustomVocative(Mutation $mutation, string $prefix, string $name, string $cacheKey): ?string
    {
        $customRule = $this->stringRepository->getByName($mutation, $prefix . mb_strtolower($name));
        if ($customRule !== null) {
            $result = $customRule->value;
            $this->saveToCache($cacheKey, $result);
            return $result;
        }

        return null;
    }

    /**
     * Converts full name (first name + last name) to vocative case
     *
     * @param string $firstName First name in nominative case
     * @param string $lastName Last name in nominative case
     * @param Mutation $mutation Language mutation
     * @param string $gender Gender (GENDER_MAN, GENDER_WOMAN, GENDER_AUTO)
     * @return string Full name in vocative case
     */
    public function vocativeFullName(string $firstName, string $lastName, Mutation $mutation, string $gender = self::GENDER_AUTO): string
    {
        // For non-Czech and non-Slovak languages, return the original full name
        if (!in_array($mutation->langCode, ['cs', 'sk'])) {
            return $firstName . ' ' . $lastName;
        }

        // Determine gender from last name if not explicitly specified
        if ($gender === self::GENDER_AUTO) {
            $gender = $this->nameVocative->getGender($lastName, true);
        }

        $firstNameVocative = $this->vocative($firstName, $mutation, $lastName, $gender);
        $lastNameVocative = $this->vocativeLastName($lastName, $mutation, $gender);

        return $firstNameVocative . ' ' . $lastNameVocative;
    }

}
