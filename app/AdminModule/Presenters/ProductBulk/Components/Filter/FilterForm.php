<?php declare(strict_types=1);

namespace App\AdminModule\Presenters\ProductBulk\Components\Filter;

use App\Model\Orm\Orm;
use App\Model\Orm\ProductVariant\ProductVariantRepository;
use App\Model\Translator;
use App\PostType\Page\Model\Orm\TreeRepository;
use App\PostType\Tag\Model\Orm\TagLocalization\TagLocalizationRepository;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;

class FilterForm extends Control
{

	public function __construct(
		private readonly array $filterSetup,
		private readonly Translator $translator,
		private readonly ProductVariantRepository $variantRepository,
		private readonly TreeRepository $treeRepository,
		private readonly TagLocalizationRepository $tagLocalizationRepository,
		private readonly Orm $orm,
	)
	{
	}

	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->add('templates', RS_TEMPLATE_DIR);

		$template->render(__DIR__ . '/filterForm.latte');
	}


	protected function createComponentForm(): Form
	{
		$form = new Form();
		$form->setTranslator($this->translator);

		$categories = $this->getErpCategoryList();

		$form->addTextArea('erpIds', 'erpCode');
		$form->addTextArea('eans', 'ean');
		$form->addSelect('erpCategory', 'erpCategory', $categories)->setPrompt('Nezvoleno');

		if (isset($this->filterSetup['eans'])) {
			$form['eans']->setDefaultValue($this->filterSetup['eans']);
		}

		if (isset($this->filterSetup['erpIds'])) {
			$form['erpIds']->setDefaultValue($this->filterSetup['erpIds']);
		}

		if (!empty($this->filterSetup['erpCategory']) && isset($categories[$this->filterSetup['erpCategory']])) {
			$form['erpCategory']->setDefaultValue($this->filterSetup['erpCategory']);
		}

		if (isset($this->filterSetup['productVariants']) && is_array($this->filterSetup['productVariants'])) {
			$productVariants = $this->variantRepository->findByIds($this->filterSetup['productVariants'])->fetchPairs('id', 'erpName');
			$form->addMultiSelect('productVariants', 'productVariants', $productVariants);
			$form['productVariants']->setDefaultValue(array_keys($productVariants));
		} else {
			$form->addMultiSelect('productVariants', 'productVariants', []);
		}

		$categories = [];
		if (isset($this->filterSetup['categories']) && is_array($this->filterSetup['categories'])) {
			$categories = $this->treeRepository->findByIds($this->filterSetup['categories'])->fetchPairs('id', 'name');
		}
		$form->addMultiSelect('categories', 'categories', $categories);
		if ($categories !== []) {
			$form['categories']->setDefaultValue(array_keys($categories));
		}

		$tags = [];
		if (isset($this->filterSetup['tags']) && is_array($this->filterSetup['tags'])) {
			$tags = $this->tagLocalizationRepository->findByIds($this->filterSetup['tags'])->fetchPairs('id', 'name');
		}
		$form->addMultiSelect('tags', 'tags', $tags);
		if ($tags !== []) {
			$form['tags']->setDefaultValue(array_keys($tags));
		}

		$brands = [];
		if (isset($this->filterSetup['brands']) && is_array($this->filterSetup['brands'])) {
			//$brands = $this->treeRepository->findByIds($this->filterSetup['brands'])->fetchPairs('id', 'name');
		}
		$form->addMultiSelect('brands', 'brands', $brands);
		if ($brands !== []) {
			$form['brands']->setDefaultValue(array_keys($brands));
		}

		$form->addSubmit('filter', 'filter');

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];
		return $form;
	}


	public function formError(Form $form): void
	{
		$this->flashMessage('Error', 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function formSucceeded(Form $form, array $data): void
	{
		$httpData = $form->getHttpData();
		assert(is_array($httpData));
		$fakedItems = ['productVariants', 'categories', 'tags', 'brands', 'erpCategory'];
		foreach ($fakedItems as $fakedItem) {
			if (isset($httpData[$fakedItem])) {
				$data[$fakedItem] = $httpData[$fakedItem];
			}
		}
		$this->presenter->redirect('this', ['filterSetup' => $data]);
	}

	private function getErpCategoryList(): array {
		$list = $this->variantRepository->getErpCategoryList();

		return array_map(function ($value) {
			return str_replace('%|%', ' / ', $value);
		}, $list);
	}
}
