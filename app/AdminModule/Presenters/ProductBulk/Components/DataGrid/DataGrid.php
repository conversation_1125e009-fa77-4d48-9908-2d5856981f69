<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\ProductBulk\Components\DataGrid;

use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Translator;
use Nette\Application\UI\Control;
use Nextras\Orm\Collection\ICollection;
use Ublaboo\DataGrid\DataSource\IDataSource;
use Ublaboo\DataGrid\Exception\DataGridException;

class DataGrid extends Control
{

	public function __construct(
		private readonly IDataSource $dataSource,
		private readonly Translator $translator,
	)
	{
	}

	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);

		$template->render(__DIR__ . '/dataGrid.latte');
	}

	/**
	 * @throws DataGridException
	 */
	public function createComponentGrid(): \Ublaboo\DataGrid\DataGrid
	{
		$grid = new \Ublaboo\DataGrid\DataGrid();
		$grid->setDataSource($this->dataSource);
		$grid->setTranslator($this->translator);
		$grid->setItemsPerPageList([10], false);

		$grid->addColumnText('id', 'id', 'id');
		$grid->addColumnText('erpCode', 'erpCode');
		$grid->addColumnText('name', 'name', 'name');
		$grid->addColumnText('availability', 'availability');
		$grid->addColumnText('category', 'category');

		//$grid->addAction('edit', 'Edit', 'Product:edit', ['id'])->setClass('btn btn-xs btn-primary');

		$grid->setDefaultSort(['name' => ICollection::ASC]);

		return $grid;
	}
}
