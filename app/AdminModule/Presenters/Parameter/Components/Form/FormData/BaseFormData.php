<?php declare(strict_types=1);

namespace App\AdminModule\Presenters\Parameter\Components\Form\FormData;

final class BaseFormData
{

	public function __construct(
		public readonly string $name,
		public readonly array $mutations,
		public readonly ?string $uid = null,
		public readonly ?string $extId = null,
		public readonly ?string $typeSort = null,
		public readonly ?bool $isProtected = false,
		public readonly ?bool $isInFilter = false,
		public readonly ?bool $isInDetail = false,
		public readonly ?string $type = null,
		public readonly ?string $productType = null,
	)
	{
	}

}
