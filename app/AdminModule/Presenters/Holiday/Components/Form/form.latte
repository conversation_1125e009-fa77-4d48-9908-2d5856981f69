{varType App\Model\Orm\Mutation\Mutation $mutation}
<form n:name="form" class="main__main" data-controller="pagemenu">
	<div class="main__header">
		{include $templates.'/part/box/header.latte',
			props: [
				hrefClose: 'default',
				img: $imgSrc,
				title: $holiday->name,
				hasGeneratedMenu: true,
				isPageTitle: true,
			]
		}
	</div>
	<div class="main__content scroll">
		{include './parts/content/content.latte', form => $form}
	</div>

	<div class="main__content-side scroll">
		{include $corePartsDirectory . '/side/state.latte', form: $form}
		{include $corePartsDirectory . '/side/btns.latte'}
	</div>
</form>

{include $templates . '/part/core/libraryOverlay.latte'}

