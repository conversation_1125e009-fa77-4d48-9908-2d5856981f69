<?php

declare(strict_types=1);

namespace App\AdminModule\Presenters\Holiday\Components\Form;

use App\AdminModule\Presenters\Holiday\Components\Form\FormData\BaseFormData;
use App\Model\ConfigService;
use App\Model\CustomField\SuggestUrls;
use App\Model\Orm\Holiday\Holiday;
use App\Model\Orm\Orm;
use App\Model\Orm\User\User;
use App\Model\Translator;
use App\Model\TranslatorDB;
use Nette\Application\UI\Control;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use const RS_TEMPLATE_DIR;

/**
 * @property-read DefaultTemplate $template
 */
final class Form extends Control
{
	public function __construct(
		private readonly Holiday $holiday,
		private readonly User $userEntity,
		private readonly Builder $formBuilder,
		private readonly Handler $formHandler,
		private readonly SuggestUrls $urls,
		private readonly Translator $translator,
		private readonly TranslatorDB $translatorDB,
		private readonly Orm $orm,
		private readonly ConfigService $configService,
	) {}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$template->add('templates', RS_TEMPLATE_DIR);
		$template->add('holiday', $this->holiday);
		$template->add('imgSrc', '');
		$template->add('corePartsDirectory', \App\PostType\Core\AdminModule\Components\Form\Form::TEMPLATE_PARTS_DIR);

		$template->orm = $this->orm;
		$template->translatorDB = $this->translatorDB;
		$template->fileUploadLink = $this->presenter->link(':Admin:File:upload');

		$template->config = $this->configService->getParams();
		$template->userEntity = $this->userEntity;

		$template->showDeleteButton = true;

		$template->urls = $this->urls;
		$template->render(__DIR__ . '/form.latte');
	}

	protected function createComponentForm(): \Nette\Application\UI\Form
	{
		$form = new \Nette\Application\UI\Form();
		$form->setMappedType(BaseFormData::class);

		$this->formBuilder->build($form, $this->holiday, $this->userEntity);

		$form->setTranslator($this->translator);
		$form->onSuccess[] = $this->formSucceeded(...);
		$form->onError[] = $this->formError(...);

		return $form;
	}

	private function formSucceeded(\Nette\Application\UI\Form $form, BaseFormData $data): void
	{
		$this->formHandler->handle($this->holiday, $data, $this->userEntity);
		$this->presenter->redirect('edit', ['id' => $this->holiday->id]);
	}

	private function formError(): void
	{
		$this->flashMessage('Error', 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	public function handleDelete(): void
	{
		$this->orm->removeAndFlush($this->holiday);
		$this->presenter->flashMessage('msg_ok_deleted', 'ok');
		$this->presenter->redirect('default');
	}

}
