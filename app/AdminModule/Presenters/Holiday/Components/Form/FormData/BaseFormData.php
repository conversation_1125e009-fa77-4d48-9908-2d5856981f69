<?php declare(strict_types=1);

namespace App\AdminModule\Presenters\Holiday\Components\Form\FormData;

use App\PostType\Core\AdminModule\Components\Form\FormData\FormDataHelper;
use App\PostType\Core\AdminModule\Components\Form\FormData\PublishFormData;
use Nextras\Dbal\Utils\DateTimeImmutable;

final class BaseFormData
{
	public DateTimeImmutable $publicFrom;
	public DateTimeImmutable $publicTo;


	public function __construct(
		public string $name,
		?string $publicFrom,
		?string $publicTo,
		public PublishFormData $publish,
	)
	{
		$this->publicFrom =	FormDataHelper::convertInvalidDateTimeValue($publicFrom);
		$this->publicTo = FormDataHelper::convertInvalidDateTimeValue($publicTo);
	}

}
