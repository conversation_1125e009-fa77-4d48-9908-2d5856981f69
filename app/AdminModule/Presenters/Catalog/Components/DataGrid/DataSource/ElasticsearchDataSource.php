<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Catalog\Components\DataGrid\DataSource;

use Elasticsearch\Client;
use Ublaboo\DataGrid\DataSource\SearchParamsBuilder;
use UnexpectedValueException;

class ElasticsearchDataSource extends \Ublaboo\DataGrid\DataSource\ElasticsearchDataSource
{

	/** @var Client */
	private $client;

	/** @phpstan-var callable(array): array */
	private $rowFactory;

	/**
	 * @phpstan-param  null|callable(array): array $rowFactory
	 */
	public function __construct(
		Client $client,
		string $indexName,
		private readonly ?array $elasticQueryMappers = [],
		?callable $rowFactory = null,
	)
	{
		$this->client = $client;
		$this->searchParamsBuilder = new SearchParamsBuilder($indexName);

		if ($rowFactory === null) {
			$rowFactory = static function (array $hit): array {
				return $hit['_source'];
			};
		}

		$this->rowFactory = $rowFactory;
	}

	public function getData(): array
	{
		$searchResult = $this->client->search($this->getQueryParameters());

		if (!isset($searchResult['hits'])) {
			throw new UnexpectedValueException();
		}

		return array_map($this->rowFactory, $searchResult['hits']['hits']);
	}

	public function getCount(): int
	{
		$searchResult = $this->client->search($this->getQueryParameters());

		if (!isset($searchResult['hits'])) {
			throw new UnexpectedValueException();
		}

		return is_array($searchResult['hits']['total'])
			? $searchResult['hits']['total']['value']
			: $searchResult['hits']['total'];
	}


	private function getQueryParameters(): array
	{
		$params = $this->searchParamsBuilder->buildParams();
		$params['body']['track_total_hits'] = true;
		$musts = $params['body']['query']['bool']['must'];
		$newMusts = [];

		foreach ($this->elasticQueryMappers as $mapper) {
			foreach ($musts as $key => $item) {
				if (($newData = $mapper->tryReMap($item)) !== null) {
					if (isset($newData[0])) {
						foreach ($newData as $newDataItem) {
							$newMusts[] = $newDataItem;
						}
					} else {
						$newMusts[] = $newData;
					}

					unset($musts[$key]);
				}
			}
		}


		$params['body']['query']['bool']['must'] = array_merge($newMusts, $musts);
		return $params;
	}

}
