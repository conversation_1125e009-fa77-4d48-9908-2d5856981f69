<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\ProductVariant\Components\Actions\ProductAddAction;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\User\User;
use Nextras\Orm\Collection\ICollection;

interface ProductAddActionFactory
{
	/**
	 * @param ICollection<ProductVariant> $variants
	 */
	public function create(
		Mutation $mutation,
		ICollection $variants,
		User $user,
	): ProductAddAction;
}
