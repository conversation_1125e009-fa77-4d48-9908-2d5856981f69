<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Elastic\Components\DisconnectedIndexDataGrid;

use App\Model\ElasticSearch\IndexModel;
use App\Model\Orm\EsIndex\EsIndexRepository;
use App\Model\Translator;
use Nette\Application\UI\Control;
use Ublaboo\DataGrid\Column\Action\Confirmation\CallbackConfirmation;
use Ublaboo\DataGrid\DataGrid;
use Ublaboo\DataGrid\Exception\DataGridException;

class DisconnectedIndexDataGrid extends Control
{

	public function __construct(
		private readonly Translator $translator,
		private readonly EsIndexRepository $esIndexRepository,
		private readonly IndexModel $indexModel,
	)
	{
	}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);

		$template->render(__DIR__ . '/disconnectedIndexDataGrid.latte');
	}


	/**
	 * @throws DataGridException
	 */
	public function createComponentGrid(): DataGrid
	{
		$grid = new DataGrid();

		$localIndexesNames = [];
		foreach ($this->esIndexRepository->findAll() as $esindex) {
			$localIndexesNames[$esindex->esName] = $esindex->esName;
		}

		$remoteIndexesWithPrefix = $this->indexModel->findElasticSearchCreatedIndexes();
		foreach ($remoteIndexesWithPrefix as $key => $remoteIndexWithPrefix) {
			if (isset($localIndexesNames[$remoteIndexWithPrefix['index']])) {
				unset($remoteIndexesWithPrefix[$key]);
			}
		}

		$grid->setPrimaryKey('index');
		$grid->setDataSource($remoteIndexesWithPrefix);

		$grid->addColumnText('esName', 'esName', 'index')->setFilterText();

		$grid->setTranslator($this->translator);

		$grid->addAction('delete', '', 'delete!')
			->setTitle('Odstranit')
			->setIcon('trash')
			->setConfirmation(
				new CallbackConfirmation(
					function ($item) {
						return 'Opravdu chcete smazat index ' . $item['index'] . '?';
					}
				)
			)
		;


		$grid->addGroupAction('Delete selected')->onSelect[] = [$this, 'deleteSelected'];

		return $grid;
	}


	public function handleDelete(string $index): void
	{
		$this->indexModel->deleteByName($index);
		$this->presenter->redirect('this');
	}

	public function deleteSelected(array $ids): void
	{
		foreach ($ids as $index) {
			$this->indexModel->deleteByName($index);
		}

		if ($this->presenter->isAjax()) {
			$this['grid']->reload();
		} else {
			$this->presenter->redirect('this');
		}
	}

}
