<li data-id="{$data->image}">
	<div class="inner">
		<div class="thumb js-handle">
			{php $img = $imageObjectFactory->getByName($data->filename, 's')}

			<span class="img"><img src="{$img->src}" alt="" /></span>
			<span class="name">{$data->name}</span>
			<a href="#" class="icon icon-close remove"></a>
		</div>
		<div class="detail">
			<div class="detail-holder">
				<div class="grid-row">
					<p class="grid-1">
						<label for="inp-title{$data->image}">{_image_name}</label><br />
						<span class="inp-fix">
							<input type="text" class="inp-text w-full" name="imageName[{$data->image}]" value="{$data->name}" id="inp-title{$data->image}" />
						</span>
					</p>
				</div>
			</div>
		</div>
		<input type="hidden" name="imageSort[{$data->image}]" value="{$data->sort}" class="inp-sort" />
	</div>
</li>
