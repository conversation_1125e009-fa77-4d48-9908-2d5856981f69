<?php declare(strict_types = 1);

namespace App\AdminModule\Components\ProductForm\DTO\Property;

class DuplicateButton
{

	private function __construct(
		public readonly string $text,
		public readonly string $icon,
	)
	{
	}

	public static function create(string $rsTemplatesPath): DuplicateButton
	{
		return new self(
			text: 'button_product_duplicate',
			icon: $rsTemplatesPath . '/part/icons/copy.svg'
		);
	}

}
