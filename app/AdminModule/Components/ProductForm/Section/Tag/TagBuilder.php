<?php

namespace App\AdminModule\Components\ProductForm\Section\Tag;

use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\PostType\Tag\Model\Orm\TagLocalization\TagLocalizationRepository;
use Nette\Forms\Container;

class TagBuilder
{
	public function __construct(
		private readonly TagLocalizationRepository $tagLocalizationRepository,
	)
	{
	}

	public function addTagsToContainer(Container $localizationContainer, ProductLocalization $localization, array $postData): void
	{
		$tagsContainer = $localizationContainer->addContainer('tags');

		if ($postData) {
			if (isset($postData['productLocalizations'][$localization->mutation->id]['tags'])) {

				foreach ($postData['productLocalizations'][$localization->mutation->id]['tags'] as $tagKey => $category) {
					$tagContainer = $tagsContainer->addContainer($tagKey);
					$tagContainer->addHidden('id');
					$tagContainer->addText('name', 'name');
					$tagContainer->addDate('from', 'from')->setDefaultValue($category['from']);
					$tagContainer->addDate('to', 'to')->setDefaultValue($category['to']);
				}
			}
		} else {
			$tagProductRelationRows = $this->tagLocalizationRepository->getProductRelations($localization->product, $localization->mutation);

			foreach ($tagProductRelationRows as $tagKey => $tagRelation) {
				$tagContainer = $tagsContainer->addContainer($tagKey);
				$tagContainer->addHidden('id', $tagRelation->tagLocalizationId);
				$tagContainer->addText('name', 'name')
					->setDefaultValue($tagRelation->name ?? $tagRelation->internalName);
				$tagContainer->addDate('from', 'from')->setDefaultValue($tagRelation->from);
				$tagContainer->addDate('to', 'to')->setDefaultValue($tagRelation->to);
			}
		}

		$tagContainer = $tagsContainer->addContainer('newItemMarker');
		$tagContainer->addHidden('id');
		$tagContainer->addText('name', 'name');
		$tagContainer->addDate('from', 'from');
		$tagContainer->addDate('to', 'to');
	}
}
