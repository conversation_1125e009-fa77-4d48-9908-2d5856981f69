{embed $templates.'/part/box/toggle.latte', props=>[
title: 'Parametry',
id: 'params',
icon: $templates.'/part/icons/sliders-h.svg',
variant: 'main',
classes: ['u-mb-xxs']
], templates=>$templates}
	{block content}

		{default $paramUidList = false}

		{embed $templates.'/part/box/toggle.latte', props=>[
		title: Parametry,
		id: 'param-product',
		open: true
		], templates=>$templates}
			{block content}
				{foreach $parameters as $param}
					{if $paramUidList}
						{continueIf !in_array($param->uid, $paramUidList)}
					{/if}

					{var $parameterValue = ($object && $object->getParameterById($param->id) && $object->getParameterValueById($param->id)) ? $object->getParameterValueById($param->id) : null}
					{if $parameterValue !== null}
						{if $parameterValue instanceof \ArrayIterator}
							{var $values = []}
							{foreach $parameterValue as $pv}
								{php $values[] = $pv->internalValue}
							{/foreach}
							{var $value = implode(', ', $values)}
						{else}
							{var $value = $parameterValue->internalValue}
						{/if}
						<div class="grid grid--y-0">
							<div class="grid__cell size--3-12">{$param->name}:</div>
							<div class="grid__cell size--9-12"><strong>{$value}</strong></div>
						</div>
					{/if}
				{/foreach}
			{/block}
		{/embed}
	{/block}
{/embed}




