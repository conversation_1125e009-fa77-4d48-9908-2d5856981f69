{var $items = []}

{foreach $form['productLocalizations']->components as $mutationId=>$localizationContainer}
	{var $mutation = $mutations->getById($mutationId)}
	{var $langCode = $mutation->langCode}

	{var $item = [
		btnsBefore: [
			[
				icon: $templates.'/part/icons/eye.svg',
				tooltip: 'Zobrazit/Skrýt',
				type: 'checkbox',
				data: [
					controller: 'Toggle',
					action: 'Toggle#changeClass LangToggles:removeToggleSelection@window->Toggle#removeToggleSelection',
					toggle-target-value: 'body',
					toggle-target-class-value: 'lang-'.$langCode,
					toggle-class-value: 'is-active',
					toggle-name-value: 'lang-'.$langCode,
					toggle-lang-toggle-value: true,
					toggle-default-langs-value: $defaultLangsStringWithLang,
					langtoggles-target: 'button',
				],
				classes: ['is-active'],
			]
		],
		texts: [
			[
				text: $mutation->name
			]
		],
		tags: [
			[
				text: $langCode
			]
		]
	]}
	{php $items[] = $item}
{/foreach}

{include $templates.'/part/box/list.latte',
	props => [
		title: 'Modifikace',
		items: $items,
		data: [
			controller: 'LangToggles',
			action: 'Toggle:langToggleSelected@window->LangToggles#langToggleSelected',
			langtoggles-default-langs-value: $defaultLangsStringWithLang,
		],
	]
}
