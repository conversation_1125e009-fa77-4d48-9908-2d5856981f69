{varType App\AdminModule\Components\PostType\Language\DTO\DTO $dto}
<p n:if="isset($dto) && $dto->languages !== []">
	<h2 class="b-std__title title">
		{_'lang_mutations'}
	</h2>
	{foreach $dto->languages as $language}
				{if $language->showPlus}
					<a n:href="duplicate!, mutationId=>$language->mutationId" class="btn btn--tag btn--grey">
						<span class="btn__text">
							{$language->name} +
						</span>
					</a>
				{else}
					<a href="{plink $dto->editAction, id=>$language->localizationId}" class="btn btn--tag">
						<span class="btn__text">
							{$language->name}
						</span>
					</a>
				{/if}
	{/foreach}
</p>

<p class="u-mb-xs" n:if="isset($dto) && $dto->duplicateButton !== null">
	<a n:href="duplicate!, mutationId=>$dto->duplicateButton->targetMutationId" class="btn btn--full btn--grey">
		<span class="btn__text item-icon">
			<span class="item-icon__icon icon">
				{include $dto->duplicateButton->icon}
			</span>
			<span class="item-icon__text">
				{_$dto->duplicateButton->text}
			</span>
		</span>
	</a>
</p>
