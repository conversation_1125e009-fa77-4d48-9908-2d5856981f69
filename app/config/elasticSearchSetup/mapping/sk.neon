
all:
	properties:
		kind:
			type: "keyword" #type: keyword - <PERSON><PERSON><PERSON><PERSON><PERSON>, jde podle toho i delat agegace
		type:
			type: "keyword" #type: keyword - <PERSON><PERSON><PERSON><PERSON><PERSON>, jde podle toho i delat agegace
		nameSort:
			type: "text"
			fielddata: true
		name:
			type: "text"
		filter:
			properties:
				publishDate:
					type: date
				availability:
					type: keyword
					#fielddata: true

superadmin:
	properties:
		kind:
			type: "keyword" #type: keyword - <PERSON><PERSON><PERSON><PERSON><PERSON>, jde podle toho i delat agegace
		type:
			type: "keyword" #type: keyword - <PERSON><PERSON><PERSON><PERSON><PERSON>, jde podle toho i delat agegace
		nameSort:
			type: "text"
			fielddata: true
		name:
			type: "text"
		filter:
			properties:
				publishDate:
					type: date
				availability:
					type: keyword
					#fielddata: true

common:
	properties:
		name:
			type: "text"
			fields:
				customEdgeNgram:
					type: text
					analyzer: 'customEdgeNgram'
		annotation:
			type: "text"
		type:
			type: "keyword"
		lastNameFirstLetter:
			type: "keyword"
		url:
			type: "keyword"
		content:
			type: "text"
			fields:
				customEdgeNgram:
					type: text
					analyzer: 'customEdgeNgram'
		publicFrom:
			type: "date"
			format: "basic_date_time_no_millis"
		publicTo:
			type: "date"
			format: "basic_date_time_no_millis"



product:
	properties:
		nameSort:
			type: "keyword"
		name:
			type: "text"
			fielddata: true
		content:
			type: "text"
		annotation:
			type: "text"
		publicFrom:
			type: "date"
			format: "basic_date_time_no_millis"
		publicTo:
			type: "date"
			format: "basic_date_time_no_millis"
		dateCreated:
			type: "date"
			format: "basic_date_time_no_millis"
		datePublished:
			type: "date"
			format: "basic_date_time_no_millis"
		classEvents:
			type: nested
			properties:
				emptyCapacity:
					type: integer
				fromLastMinuteLimit:
					type: integer
				emptyCapacityLastMinuteLimit:
					type: integer
				from:
					type: date
					format: basic_date_time_no_millis
		customFeed:
			enabled: false
		customFeedAvailability:
			enabled: false
		6MonthSale:   # add for order
			type: integer
		score:
			type: float

		fulltext-name:
			type: "text"
			fielddata: true
			fields:
				customEdgeNgram:
					type: text
					analyzer: 'customEdgeNgram'
		fulltext-content:
			type: "text"
			fielddata: true
			fields:
				customEdgeNgram:
					type: text
					analyzer: 'customEdgeNgram'
		fulltext-categories:
			type: "text"
			fielddata: true
			fields:
				customEdgeNgram:
					type: text
					analyzer: 'customEdgeNgram'

