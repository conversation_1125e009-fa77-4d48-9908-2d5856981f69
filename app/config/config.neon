#
# SECURITY WARNING: it is CRITIC<PERSON> that this file & directory are NOT accessible directly via a web browser!
#
# If you don't protect this directory from direct web access, anybody will be able to see your passwords.
# http://nette.org/security-warning
#
parameters:
	stageName: 'dev'
	postTypeRoutes: []

	google:
		oauth:
			isPublic: true
			clientId: 308496718065-6svmsailet17pa64euqavt39m23fsgq9.apps.googleusercontent.com
			clientSecret: 'ask_programmer'

	config:
		identityMapDebug: false
		projectName: 'omega'
		defaultLang: 'cs'

		imageFromStage: null
		envName: %stageName%
		consoleMode: %consoleMode%
		adminAlias: superadmin # pri zmene aliasu je potreba upravit htaccess RewriteCond %{REQUEST_URI} !/superadmin/.* ; pripadne pridat presmerovani RewriteRule ^superadmin/(.*)$ new/$1 [R=301,QSA,L]

		adminTitle: Superadmin
		adminLang: cz # cz, en
		debuggerEditor: 'phpstorm://open?file=%file&line=%line'

		translations:
			insertNew: false
			markUsage: false

		socialLogin:
				cookieName: 'domain-social-login'
				facebook:
					isEnabdled: true
					params:
						appId: '1440426033193434'
						appSecret: 'ask_programmer'
					scope: ['email']
				seznam:
					isEnabdled: true
					params:
						clientId: '1e78f563c4d1b0d2ad6393fae5fdd63579f631ed4f2b5d21'
						clientSecret: 'ask_programmer'

		wwwDir: %wwwDir%
		mailChimp:
			api: ""
			listId: ""

		google:
			apiKey: 'AIzaSyAiwUleiQ0L9WVJZZc3Z4f7O3RRf17sCsM' # SK
			# apiKey: ??? # client


		userGroup: # nastavení práv pro už. skupiny

			admin: {
				setPermission: TRUE,
				sources: {}
				denySources: {
					Admin:Developer: []
					Admin:Elastic: []
					Admin:Cache: []
					Admin:ApiToken: []
				}
			}
			user: { #registered
				setPermission: TRUE,
				sources: {Admin:Sign: []}
				denySources: {}
			}

		userRoles:
			user: Registrovaný
			admin: Admin
			developer: Developer

		templatesProduct: {
			:Front:Product:detail: Product
		}


		voucherKinds:
			normal: voucher_kind_normal
			product: voucher_kind_product

		voucherTypes:
			amount: voucher_amount
			percent: voucher_percent
			freeDelivery: voucher_freeDelivery
			amountCombination: voucher_amountCombination

		voucherApplications:
			product: voucher_app_product
			service: voucher_app_service
			all: voucher_app_all

di:
	lazy: false

php:
	date.timezone: Europe/Prague
#	zlib.output_compression: yes

mail:
	smtp: true


application:
	errorPresenter: Front:Error
	mapping:
		*: [App, *Module\Presenters, *\*Presenter]

tracy:
	maxDepth: 4

http:
	headers:
		X-Powered-By: null
#	cookieDomain: domain

session:
	expiration: 14 days
	cookie_secure: true ##TODO doesnt-work
#	autoStart: true

extensions:
	logging: Contributte\Logging\DI\TracyLoggingExtension
	monolog: Contributte\Monolog\DI\MonologExtension

logging:
	logDir: %appDir%/../nettelog


includes:
	- ../Exponea/exponea.neon
	- loggers.neon
	- events.neon
	- templates.neon
	- monolog.neon
	#- mutations.neon
	- services.neon
	- redis.neon
	- amqp.neon
	- scheduler.neon ## must be after redis and before messenger
	- console.neon
	- messenger.neon
	- admin.neon
	- images.neon
	- web.neon
	- webVersion.neon
	- shop.neon
	- migrations.neon
	- components.neon
	- orm.neon
	- erp.neon
	- customFields.neon
	- customContent.neon
	- elasticSearch.neon
	- robotsTxt.neon
	- postType.neon
	- state.neon
	- api.neon
	- basicAuth.neon
	- bucketFilter.neon
	- sentry.php
	- ../FrontModule/Components/config.neon
	- ../AdminModule/config.neon
	- ../Model/StaticPage/config.neon
	- priceLogger.neon
	- ../Model/FeedGenerator/Individual/config.neon
	- ../Model/FeedGenerator/Core/config.neon
	- openGraph.neon
