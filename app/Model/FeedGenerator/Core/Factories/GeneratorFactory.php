<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Core\Factories;

use App\Model\FeedGenerator\Core\Exceptions\GeneratorException;
use App\Model\FeedGenerator\Core\Generators\GeneratorInterface;
use App\Model\FeedGenerator\Core\Generators\Heureka\HeurekaGenerator;

class GeneratorFactory extends AbstractFactory implements GeneratorFactoryInterface
{

	public function __construct(
		private readonly HeurekaGenerator $heurekaGenerator,
	)
	{
	}

	public function heurekaGenerator(): GeneratorInterface
	{
		return $this->heurekaGenerator
			->setDispatcher($this->getFactory()->dispatcherFactory()->heurekaDispatcher());
	}

	public function customFeedGenerator(): GeneratorInterface
	{
		throw new GeneratorException(GeneratorException::NOT_IMPLEMENTED);
	}

	public function sitemapGenerator(): GeneratorInterface
	{
		throw new GeneratorException(GeneratorException::NOT_IMPLEMENTED);
	}
	public function sitemapRootGenerator(): GeneratorInterface
	{
		throw new GeneratorException(GeneratorException::NOT_IMPLEMENTED);
	}

}
