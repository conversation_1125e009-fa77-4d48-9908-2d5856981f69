<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Individual\Factories;

use App\Model\FeedGenerator\Core\Exceptions\ProviderException;
use App\Model\FeedGenerator\Core\Factories\AbstractFactory;
use App\Model\FeedGenerator\Core\Factories\ProviderFactoryInterface;
use App\Model\FeedGenerator\Core\Providers\ProviderInterface;
use App\Model\FeedGenerator\Individual\Providers\CustomFeedProvider;
use App\Model\FeedGenerator\Individual\Providers\SitemapProvider;

final class IndividualProviderFactory extends AbstractFactory implements ProviderFactoryInterface
{

	public function __construct(
		private readonly CustomFeedProvider $customFeedProvider,
		private readonly SitemapProvider $sitemapProvider,
	)
	{
	}

	public function heurekaProvider(): ProviderInterface
	{
		throw new ProviderException(ProviderException::NOT_IMPLEMENTED);
	}

	public function customFeedProvider(): ProviderInterface
	{
		return $this->customFeedProvider->setMutationCode(
			$this->getFactory()->getMutationCode()
		);
	}
	public function sitemapProvider(): ProviderInterface
	{
		return $this->sitemapProvider->setMutationCode(
			$this->getFactory()->getMutationCode()
		);
	}

}
