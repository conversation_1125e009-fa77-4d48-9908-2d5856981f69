<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Individual;

use App\Model\FeedGenerator\Core\Factories\FactoryInterface;
use App\Model\FeedGenerator\Core\FeedGenerator;
use App\Model\FeedGenerator\Individual\Generators\Sitemap\SitemapGenerator;
use App\Model\FeedGenerator\Individual\Generators\Sitemap\SitemapRootGenerator;
use App\Model\FeedGenerator\Individual\Providers\SitemapProvider;

final class IndividualFeedGenerator extends FeedGenerator
{

	public function __construct(
		FactoryInterface $factory,
		?string $mutationCode = null,
	)
	{
		parent::__construct($factory, $mutationCode);
	}

	public function generateCustomFeed(string $outputFile): void
	{
		$this->getFactory()->generatorFactory()
			->customFeedGenerator()
			->setOutputXml($outputFile)
			->generate()
			->save()
			->moveTmpOutput();
	}
	public function generateSitemap(\Generator $yieldGenerator, string $outputFile): SitemapGenerator
	{
		$sitemapGenerator = $this->getFactory()->generatorFactory()->sitemapGenerator();
		assert($sitemapGenerator instanceof SitemapGenerator);
		$provider = $sitemapGenerator->getDispatcher()->getProvider();
		assert($provider instanceof SitemapProvider);
		$provider->setYieldGenerator($yieldGenerator);
		$sitemapGenerator->setOutputXml($outputFile)->generate();
		if ($sitemapGenerator->getDtosCount() > 0) {
			$sitemapGenerator->save();
		}

		return $sitemapGenerator;
	}
	public function generateRootSitemap(\Generator $yieldGenerator, string $outputFile): SitemapRootGenerator
	{
		$sitemapRootGenerator = $this->getFactory()->generatorFactory()->sitemapRootGenerator();
		assert($sitemapRootGenerator instanceof SitemapRootGenerator);
		$provider = $sitemapRootGenerator->getDispatcher()->getProvider();
		assert($provider instanceof SitemapProvider);
		$provider->setYieldGenerator($yieldGenerator);
		$sitemapRootGenerator->setOutputXml($outputFile)->generate();
		if ($sitemapRootGenerator->getDtosCount() > 0) {
			$sitemapRootGenerator->save();
		}

		return $sitemapRootGenerator;
	}

}
