<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Individual\DTO\CustomFeed;

use App\Model\FeedGenerator\Core\DTO\CountryInterface;
use App\Model\FeedGenerator\Core\DTO\CustomFeed\ProductCustomFeedInterface;
use App\Model\FeedGenerator\Core\DTO\DTO;
use Nette\Utils\DateTime;

/**
 * @method Product setCountry(CountryInterface $country)
 * @method Country getCountry()
 */
final class Product extends DTO implements ProductCustomFeedInterface
{

	use HasCountry;

	final public function getIsNew(): int
	{
		return (int) ($this->getData()['isNew'] ?? 0);
	}

	final public function getIsTop(): int
	{
		return (int) ($this->getData()['isTop'] ?? 0);
	}

	final public function getIsAction(): int
	{
		return (int) ($this->getData()['isInDiscount'] ?? 0);
	}

	final public function getIsGift(): int
	{
		return (int) ($this->getData()['customFeed']['isGift'] ?? 0);
	}

	final public function getIsUsed(): int
	{
		return (int) ($this->getData()['isDamaged'] ?? 0);
	}

	final public function getIsDeliveryFree(): int
	{
		return (int) ($this->getData()['customFeed']['isFreeTransit'] ?? 0);
	}

	final public function getCode(): string
	{
		return $this->getData()['customFeed']['code'] ?? '';
	}

	final public function getWeight(): int|null
	{
		return $this->getData()['customFeed']['weight'] ?? null;
	}

	final public function getHeight(): int|null
	{
		return $this->getData()['customFeed']['height'] ?? null;
	}

	final public function getWidth(): int|null
	{
		return $this->getData()['customFeed']['width'] ?? null;
	}

	final public function getDepth(): int|null
	{
		return $this->getData()['customFeed']['depth'] ?? null;
	}

	final public function getPageCount(): int|null
	{
		$pageCount = $this->getData()['customFeed']['pageCount'] ?? null;

		if ($pageCount === null) {
			return null;
		}

		if ($pageCount === 0) {
			return null;
		}

		return $pageCount;
	}

	final public function getDatePublication(): string
	{
		if (!isset($this->getData()['customFeed']['publicationDate'])) {
			return '';
		}

		return DateTime::from($this->getData()['customFeed']['publicationDate'])->format('Y.m.d');
	}

	/**
	 * @return Language[]
	 */
	final public function getLanguages(): array
	{
		if (!isset($this->getData()['customFeed']['languages'])) {
			return [];
		}

		$languages = [];
		foreach ($this->getData()['customFeed']['languages'] as $language) {
			$languages[] = Language::createInstance()->setData($language);
		}

		return $languages;
	}

	final public function getScore(): float
	{
		return $this->getData()['score'] ?? 0;
	}

	final public function getProductId(): string
	{
		return (string) ($this->getData()['id'] ?? '');
	}

	final public function getUrl(): string
	{
		return $this->getData()['url'] ?? '';
	}

	final public function getTitle(): string
	{
		return $this->getData()['nameTitle'] ?? '';
	}

	final public function getDescriptionShort(): string
	{
		return $this->getData()['annotation'] ?? '';
	}

	final public function getDescriptionLong(): string
	{
		return $this->getData()['content'] ?? '';
	}

	final public function getEan(): string
	{
		return $this->getData()['eans'][0] ?? '';
	}

	public function getType(): string|null
	{
		return $this->getData()['customFeed']['productType'] ?? null;
	}

	final public function getManufacturer(): Manufacturer|null
	{
		if (!isset($this->getData()['customFeed']['manufacturer'])) {
			return null;
		}

		return Manufacturer::createInstance()->setData($this->getData()['customFeed']['manufacturer']);
	}

	/**
	 * @return Gift[]
	 */
	final public function getGifts(): array
	{
		if (!isset($this->getData()['customFeed']['gifts'])) {
			return [];
		}

		$gifts = [];

		foreach ($this->getData()['customFeed']['gifts'] as $gift) {
			$gifts[] = Gift::createInstance()->setData($gift);
		}

		return $gifts;
	}

	final public function getAvailability(CountryInterface $country): Availability
	{
		return Availability::createInstance()->setCountry($country)->setData($this->getData());
	}

	final public function getCategory(): Category
	{
		return Category::createInstance()->setData($this->getData());
	}

	final public function getImage(): Image
	{
		return Image::createInstance()->setData($this->getData());
	}

	/**
	 * @return Delivery[]
	 */
	final public function getDeliveries(): array
	{
		$deliveries = [];

		foreach ($this->getData()['customFeed']['shipments'][$this->getCountry()->getCountyCode()] ?? [] as $shipmentPrice) {
			$deliveries[] = Delivery::createInstance()->setData($shipmentPrice);
		}

		return $deliveries;
	}

	/**
	 * @return Preview[]
	 */
	final public function getPreviews(): array
	{
		if (!isset($this->getData()['customFeed']['previewsUrl'])) {
			return [];
		}

		$previews = [];
		foreach ($this->getData()['customFeed']['previewsUrl'] as $previewUrlData) {
			$previews[] = Preview::createInstance()->setData($previewUrlData);
		}

		return $previews;
	}

	/**
	 * @return Author[]
	 */
	public function getAuthors(): array
	{
		if (!isset($this->getData()['customFeed']['authors'])) {
			return [];
		}

		$authors = [];

		foreach ($this->getData()['customFeed']['authors'] as $authorData) {
			$authors[] = Author::createInstance()->setData($authorData);
		}

		return $authors;
	}

	public function getAuthor(mixed $data): Author
	{
		return Author::createInstance()->setData($data);
	}

	final public function getPrice(CountryInterface $country): Price
	{
		return Price::createInstance()->setCountry($country)->setData($this->getData());
	}

	final public function getStock(): Stock
	{
		return Stock::createInstance()->setData($this->getData());
	}

	final public function getAlternativeProducts(): array
	{
		if (!isset($this->getData()['customFeed']['alternativeProductsCode'])) {
			return [];
		}

		$alternativeProducts = [];

		foreach ($this->getData()['customFeed']['alternativeProductsCode'] as $alternativeProductData) {
			$alternativeProducts[] = AlternativeProduct::createInstance()->setData($alternativeProductData);
		}

		return $alternativeProducts;
	}

	final public function getRelatedProducts(): array
	{
		if (!isset($this->getData()['customFeed']['relatedProductsCode'])) {
			return [];
		}

		$relatedProducts = [];

		foreach ($this->getData()['customFeed']['relatedProductsCode'] as $relatedProductData) {
			$relatedProducts[] = AlternativeProduct::createInstance()->setData($relatedProductData);
		}

		return $relatedProducts;
	}

}
