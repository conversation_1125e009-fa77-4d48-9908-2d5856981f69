<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Individual\DTO\CustomFeed;

use App\Model\FeedGenerator\Core\DTO\DTO;
use App\Model\FeedGenerator\Core\DTO\DTOInterface;

/**
 * @method static RelativeProduct createInstance()
 * @method RelativeProduct setData(mixed $data)
 */
final class RelativeProduct extends DTO implements DTOInterface
{

	final public function getId(): int
	{
		return $this->getData();
	}

}
