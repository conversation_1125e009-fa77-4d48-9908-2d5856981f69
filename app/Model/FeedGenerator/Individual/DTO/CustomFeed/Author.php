<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Individual\DTO\CustomFeed;

use App\Model\FeedGenerator\Core\DTO\DTO;
use App\Model\FeedGenerator\Core\DTO\DTOInterface;

/**
 * @method static Author createInstance()
 * @method Author setData(mixed $data)
 */
final class Author extends DTO implements DTOInterface
{

	final public function getName(): string|null
	{
		return $this->getData()['name'] ?? null;
	}

	final public function getId(): int|null
	{
		return $this->getData()['id'] ?? null;
	}

}
