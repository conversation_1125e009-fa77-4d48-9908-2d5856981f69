<?php
declare(strict_types = 1);

namespace App\Model;


use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Price;
use App\Model\Orm\Product\Product;
use Brick\Math\RoundingMode;
use Brick\Money\Money;

final class PriceInfo {

	private ?string $tag = null;
	private ?float $discountPercentage = null;
	private ?float $discountPercentageSort = null;
	private ?Money $discountMoney = null;
	private ?Money $originalPrice = null;

	private ?string $type = null;
	private Money $sellingPrice;

	public function __construct(
		Product $product,
		Mutation $mutation,
		private readonly string $currency,
		private readonly Money $standardPrice,
		private readonly Money $recommendedPrice,
		private readonly Money $referencePrice,
		private readonly ?Money $discountPrice,
	)
	{
		$this->init($product, $mutation);
	}

	private function init(Product $product, Mutation $mutation): void
	{
		$this->tag = $product->getPriceTag($mutation);
		$this->sellingPrice = $this->discountPrice ?? $this->standardPrice;


		$diffPercentage = $this->getDifferencePercentage($this->referencePrice, $this->sellingPrice);
		$diffAmount = $this->getDifferenceAmount($this->referencePrice, $this->sellingPrice);

		$this->discountPercentageSort = $diffPercentage;

		// type A
		if (
			(($this->recommendedPrice->isGreaterThan($this->sellingPrice->getAmount()) && $this->getDifferencePercentage($this->recommendedPrice, $this->sellingPrice) > 50) || $this->hasDiscountTag()) &&
			($this->referencePrice->isGreaterThan($this->sellingPrice->getAmount()) && ($this->getDifferencePercentage($this->referencePrice, $this->sellingPrice) > 5 || $this->getDifferenceAmount($this->referencePrice, $this->sellingPrice) > 100) )
		) {
			$this->type = 'A';
			$this->originalPrice = $this->referencePrice;
			$this->discountPercentage = $this->getDifferencePercentage($this->referencePrice, $this->sellingPrice);
			// type B
		} elseif (($this->recommendedPrice->isGreaterThan($this->sellingPrice->getAmount()) && $this->getDifferencePercentage($this->recommendedPrice, $this->sellingPrice) > 50) || $this->hasDiscountTag()) {
			$this->type = 'B';
			$this->discountMoney = Price::from(Money::of($this->getDifferenceAmount($this->recommendedPrice, $this->sellingPrice), $this->currency))->asMoney();
			$this->discountPercentageSort = $this->getDifferencePercentage($this->recommendedPrice, $this->sellingPrice);
			// type C
		} elseif (($this->referencePrice->isGreaterThan($this->sellingPrice->getAmount()) && ($diffPercentage > 5 || $diffAmount > 100))) {
			$this->type = 'C';
			$this->originalPrice = $this->referencePrice;

			if ($diffPercentage > 5 && $diffAmount <= 100) {
				$this->discountPercentage = $diffPercentage;
			} elseif ($diffAmount > 100) {
				$this->discountMoney = Price::from(Money::of($diffAmount, $this->currency))->asMoney();
			}

			// type E
		} elseif ($this->recommendedPrice->isGreaterThan($this->sellingPrice->getAmount())) {
			$this->type = 'E';
			$this->discountMoney = Price::from(Money::of($this->getDifferenceAmount($this->recommendedPrice, $this->sellingPrice), $this->currency))->asMoney();
			$this->discountPercentageSort = $this->getDifferencePercentage($this->recommendedPrice, $this->sellingPrice);
		}

		// TODO: type D - club prices
	}


	private function getDifferencePercentage(Money $price, Money $toDiff): float
	{
		if ($price->isZero()) {
			return 0;
		}
		return round(100 * ($price->getAmount()->toFloat() - $toDiff->getAmount()->toFloat()) / $price->getAmount()->toFloat(), 2);
	}

	private function getDifferenceAmount(Money $price, Money $toDiff): float
	{
		return round($price->getAmount()->toFloat() - $toDiff->getAmount()->toFloat(),2);
	}

	private function hasDiscountTag(): bool
	{
		return $this->tag !== null;
	}

	public function getTag(): ?string
	{
		return $this->hasDiscountTag() ? $this->tag : null;
	}

	public function getOriginalPrice(): ?Money
	{
		return $this->originalPrice;
	}

	public function getDiscountPercentage(): ?float
	{
		if ($this->discountPercentage !== null) {
			return round($this->discountPercentage);
		}
		return null;
	}

	public function getDiscountPercentageSort(): ?float
	{
		if ($this->discountPercentageSort !== null) {
			return round($this->discountPercentageSort);
		}
		return null;
	}

	public function getDiscountAmount(): ?Money
	{
		return $this->discountMoney;
	}

	public function getDiscountDelta(): ?Money
	{
		if ($this->getOriginalPrice() !== null) {
			if($this->discountPercentage !== null && $this->originalPrice !== null){
				return $this->originalPrice->minus($this->standardPrice);
			}

			if(($discountAmount = $this->getDiscountAmount()) !== null){
				return $discountAmount;
			}
		}
		return null;
	}

	public function getType(): ?string
	{
		return $this->type;
	}

	public function getCurrencyCode(): string
	{
		return $this->currency;
	}

	public function getRecommendedPrice(): Money
	{
		return $this->recommendedPrice;
	}

	public function getReferencePrice(): Money
	{
		return $this->referencePrice;
	}
	public function getSellingPrice(): Money
	{
		return $this->sellingPrice;
	}

	public function getStandardPrice(): Money
	{
		return $this->standardPrice;
	}

	public function getOldPrice(): ?Money
	{
		if ($this->discountPrice !== null && $this->discountPrice->isLessThan($this->standardPrice->getAmount())) {
			return $this->getStandardPrice();
		}
		return null;
	}

	public function getOldPriceDiscountPercentage(): ?int
	{
		$oldPrice = $this->getOldPrice()?->getAmount();
		if ($oldPrice=== null) {
			return null;
		}
		$price = $this->getSellingPrice()->getAmount();

		$discount = $oldPrice->minus($price)
		                     ->dividedBy($oldPrice, 2, RoundingMode::HALF_UP)
		                     ->multipliedBy(100);

		return $discount->toInt();
	}

}
