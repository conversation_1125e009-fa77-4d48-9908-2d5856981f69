<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\ElasticItem;

use Elastica\Query\AbstractQuery;
use Elastica\Query\Range;
use Elastica\Query\Term;
use Elastica\QueryBuilder;

class ProductIds implements ElasticItem, QuestionableElasticItem
{
	private const ELASTIC_KEY = '_id';

	public function __construct(private readonly array $productIds)
	{
	}

	public function getElasticKey(): string
	{
		return self::ELASTIC_KEY;
	}

	public function getCondition(): AbstractQuery
	{
		$notQueryBuilder = new QueryBuilder();
		return $notQueryBuilder->query()->terms(self::ELASTIC_KEY, $this->productIds);
	}
}
