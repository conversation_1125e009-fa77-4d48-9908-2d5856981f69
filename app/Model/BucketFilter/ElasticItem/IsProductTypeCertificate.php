<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\ElasticItem;

use Elastica\Query\AbstractQuery;
use Elastica\Query\Term;
use Elastica\QueryBuilder;

class IsProductTypeCertificate implements ElasticItem, QuestionableElasticItem
{

	public const ELASTIC_KEY = 'productType';

	public function getCondition(): AbstractQuery
	{
		$b = new QueryBuilder();
		$condition = $b->query()->bool();
		$preorder = new Term();
		$preorder->setTerm(self::ELASTIC_KEY, 'CERTIFICATE');
		$condition->addMust($preorder);

		return $condition;
	}

	public function getElasticKey(): string
	{
		return self::ELASTIC_KEY;
	}

}
