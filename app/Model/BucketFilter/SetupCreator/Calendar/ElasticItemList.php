<?php declare(strict_types=1);

namespace App\Model\BucketFilter\SetupCreator\Calendar;

use App\Model\BucketFilter\ElasticItem\DiscreteValues;
use App\Model\BucketFilter\ElasticItem\MultiValue;
use App\Model\BucketFilter\QueryFilter;
use App\Model\BucketFilter\SetupCreator\ElasticItemListGenerator;
use App\Model\Orm\Mutation\Mutation;
use App\PostType\CalendarTag\Model\Orm\CalendarTag\CalendarTagLocalizationRepository;
use App\PostType\Faculty\Model\Orm\FacultyLocalizationRepository;

final class ElasticItemList implements ElasticItemListGenerator
{

	private array $list;

	public function __construct(
		private readonly Mutation $mutation,
		private readonly QueryFilter $queryFilter,
		private readonly array $allSelectedParameters,
		private readonly CalendarTagLocalizationRepository $calendarTagLocalizationRepository,
	)
	{
	}


	public function getElasticItemList(): array
	{
		if (!isset($this->list)) {
			$this->list = $this->getItemsForCatalog();
		}

		return $this->list;
	}


	private function getItemsForCatalog(): array
	{
		$filterItems = [];

		$name = 'tags';
		$filterItems[] = new MultiValue(
			elasticKey: $name,
			selectedValues: $this->allSelectedParameters[DiscreteValues::NAMESPACE_DIALS][$name] ?? [],
			getOptionFunction: fn(): array => $this->calendarTagLocalizationRepository->findForSelectList($this->mutation),
			queryFilter: $this->queryFilter,
			typeInElasticEnforcer: fn($value) => (int)$value
		);

		return $filterItems;
	}
}
