<?php declare(strict_types=1);

namespace App\Model\BucketFilter\SetupCreator\Discount;

use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\State\State;
use App\PostType\Discount\Model\Orm\DiscountLocalization\DiscountLocalization;

interface ElasticItemListFactory
{

	public function create(
		DiscountLocalization $parameterObject,
		State $currentState,
		PriceLevel $priceLevel,
		array $allSelectedParameters,
	): ElasticItemList;

}
