<?php

declare(strict_types=1);

namespace App\Model\BucketFilter;

use App\Model\CustomField\LazyValue;
use App\Model\Orm\Orm;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\Routable;
use App\Model\Orm\Traits\HasStaticCache;
use App\PostType\Page\Model\Orm\Tree;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;

final class CatalogParameter
{

	use HasStaticCache;

	public function __construct(
		private readonly Orm $orm,
	)
	{
	}

	public function getParametersCfForSpecialFilter(Routable $tree): mixed
	{
		if (isset($tree->cf->parameterForFilter->specialFilters) && $tree->cf->parameterForFilter->specialFilters) {
			return $tree->cf->parameterForFilter->specialFilters;
		} else {
			if (isset($tree->pathItems)) {
				foreach (array_reverse($tree->pathItems) as $item) {
					if (isset($item->cf->parameterForFilter->specialFilters) && $item->cf->parameterForFilter->specialFilters) {
						return $item->cf->parameterForFilter->specialFilters;
					}
				}
			}
		}
		return null;
	}
	public function getParametersCfForFilter(Routable|null $tree): mixed
	{
		$generator = function() use ($tree) {
			if ($tree === null){
				return null;
			}

			if (isset($tree->cf->parameterForFilter->visibleParameters) && $tree->cf->parameterForFilter->visibleParameters) {
				return $tree->cf->parameterForFilter;
			} else {
				if (isset($tree->pathItems)) {
					foreach (array_reverse($tree->pathItems) as $item) {
						if (isset($item->cf->parameterForFilter->visibleParameters) && $item->cf->parameterForFilter->visibleParameters) {
							return $item->cf->parameterForFilter;
						}
					}
				}
			}

			return null;
		};

		return $this->tryLoadCache($this->createCacheKey('getParametersCfForFilter', $tree), $generator);
	}

	public function getParametersCfForProductDetail(Tree|null $tree): mixed
	{
		if ($tree === null) {
			return null;
		}

		if (isset($tree->cf->categoryParametersForProductDetail->visibleParameters) && $tree->cf->categoryParametersForProductDetail->visibleParameters) {
			return $tree->cf->categoryParametersForProductDetail;
		} else {
			foreach (array_reverse($tree->pathItems) as $item) {
				if (isset($item->cf->categoryParametersForProductDetail->visibleParameters) && $item->cf->categoryParametersForProductDetail->visibleParameters) {
					return $item->cf->categoryParametersForProductDetail;
				}
			}
		}

		return null;
	}


	/**
	 * @return ICollection<Parameter>
	 */
	public function getPossibleParametersForCatalog(Routable $tree): ICollection
	{
		$parametersCf = $this->getParametersCfForFilter($tree);

		/** @var ICollection<Parameter> $possibleParameters */
		$possibleParameters = new EmptyCollection();

		if (isset($parametersCf->visibleParameters) && $visibleParameters = $parametersCf->visibleParameters) {
			$visibleParameterUids = [];
			foreach ($visibleParameters as $visibleParameter) {
				if (isset($visibleParameter->parameter) && $visibleParameter->parameter instanceof LazyValue && $parameter = $visibleParameter->parameter->getEntity()) {
					assert($parameter instanceof Parameter);
					$visibleParameterUids[] = $parameter->uid;
				}
			}

			if ($visibleParameterUids) {
				$possibleParameters = $this->orm->parameter->findByUidOrdered($visibleParameterUids)->findBy(['isInFilter' => 1]);
			}
		}

		return $possibleParameters;
	}

	/**
	 * @return ICollection<Parameter>
	 */
	public function getPossibleIndexableParametersForCatalog(Tree $tree): ICollection
	{
		$parametersCf = $this->getParametersCfForFilter($tree);

		/** @var ICollection<Parameter> $possibleIndexableParameters */
		$possibleIndexableParameters = new EmptyCollection();

		if (isset($parametersCf->visibleParameters) && $visibleParameters = $parametersCf->visibleParameters) {

			$indexableParameterUids = [];
			foreach ($visibleParameters as $visibleParameter) {

				if (
					isset($visibleParameter->indexable)
					&& $visibleParameter->indexable
					&& isset($visibleParameter->parameter)
					&& $visibleParameter->parameter instanceof LazyValue
					&& $parameter = $visibleParameter->parameter->getEntity()) {
					assert($parameter instanceof Parameter);
					$indexableParameterUids[] = $parameter->uid;
				}
			}

			if ($indexableParameterUids) {
				$possibleIndexableParameters = $this->orm->parameter->findByUidOrdered($indexableParameterUids)->findBy(['isInFilter' => 1]);
			}
		}

		return $possibleIndexableParameters;
	}

}
