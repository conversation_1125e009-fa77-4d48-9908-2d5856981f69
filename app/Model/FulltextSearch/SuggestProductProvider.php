<?php declare(strict_types=1);

namespace App\Model\FulltextSearch;

use App\Model\Consent\MarketingConsent;
use App\Model\ElasticSearch\Product\Repository;
use App\Model\LastVisitedProduct;
use App\Model\Orm\Mutation\Mutation;

readonly class SuggestProductProvider
{

	public function __construct(
		private Mutation $mutation,
		private Repository $esProductRepository,
		private LastVisitedProduct $lastVisitedProduct,
		private MarketingConsent $marketingConsent,
	)
	{
	}

	public function getByUser(): \App\Model\BucketFilter\Result
	{
		if ($this->marketingConsent->isPersonalizationGranted()) {
			$productCollection = $this->lastVisitedProduct->getProductCollection();
			$products = $productCollection->limitBy(10);

			if ($products->count()) {
				return \App\Model\BucketFilter\Result::from(
					$products,
					$products->count(),
					$products->count(),
				);
			}
		}

		return \App\Model\BucketFilter\Result::empty();
	}

	public function countByUser(): int
	{
		return count($this->lastVisitedProduct->getProductIds());
	}

	public function getTopProducts(): \App\Model\BucketFilter\Result
	{
		return $this->esProductRepository->findTopProduct($this->mutation);
	}

}
