<?php declare(strict_types=1);

namespace App\Model\Erp\Processor\Reader;

use App\Model\Erp\Exception\SkippedException;
use App\Model\Orm\ImportCache\ImportCache;
use App\Model\Orm\Orm;
use App\PostType\Page\Model\Orm\TreeAlternative;
use Nette\Utils\ArrayHash;
use Nette\Utils\Json;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Psr\Log\LoggerInterface;

final class AlternativeCategoryReader implements Reader
{

	public function __construct(
		private readonly Orm $orm,
	)
	{
	}

	public function init(): void
	{
		$this->orm->setPublicOnly(false);
	}

	public function read(ImportCache $importCache, LoggerInterface $logger): void
	{
		assert($importCache->type === ImportCache::TYPE_ALTERNATIVE_CATEGORY);
		$treeAlternative = $this->orm->treeAlternative->getBy(['extId' => $importCache->extId]);

		if ($treeAlternative === null) {
			$treeAlternative = new TreeAlternative();
		}

		if ($this->orm->importCache->findBy(['type' => $importCache->type, 'status' => [ImportCache::STATUS_READY], 'extId' => $importCache->extId])->countStored() > 0) {
			throw new SkippedException('Newer import pending.');
		}

		if ($treeAlternative->syncChecksum === $this->createChecksum($importCache->data)) {
			throw new SkippedException('Checksum is same.');
		}

		$treeAlternative->alt_id = (int) $importCache->data->alt_id;
		$treeAlternative->type = $importCache->data->type;
		$treeAlternative->alt_name = $importCache->data->alt_name;
		$treeAlternative->alt_path = $importCache->data->alt_path;
		$treeAlternative->syncChecksum = $this->createChecksum($importCache->data);
		$treeAlternative->syncTime = new DateTimeImmutable();
		$treeAlternative->extId = $importCache->extId;

		$this->orm->persistAndFlush($treeAlternative);
	}


	private function createChecksum(ArrayHash $data): string
	{
		return md5(Json::encode($data));
	}

}
