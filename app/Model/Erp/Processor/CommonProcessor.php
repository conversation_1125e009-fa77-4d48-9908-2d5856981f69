<?php
declare(strict_types = 1);

namespace App\Model\Erp\Processor;

use App\Model\ConfigService;
use App\Model\Erp\Exception\InvalidTypeException;
use App\Model\Erp\Exception\SkippedException;
use App\Model\Erp\Exception\WarningException;
use App\Model\Erp\Processor\Batch\Result;
use App\Model\Erp\Traits\HasLogger;
use App\Model\Orm\ImportCache\ImportCache;
use App\Model\Orm\Orm;
use App\Model\Sentry\SentryLogger;
use Closure;
use Contributte\Logging\ILogger;
use Contributte\Monolog\LoggerManager;
use Nette\Utils\ArrayHash;
use Nette\Utils\FileSystem;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Throwable;
use <PERSON>\Debugger;
use <PERSON>\Logger;

class CommonProcessor
{

	use HasLogger;

	protected ArrayHash $config;

	protected LoggerInterface $logger;

	protected bool $logToSentry = false;

	public function __construct(
		protected readonly string $type,
		private readonly Orm $orm,
		private readonly SentryLogger $sentryLogger,
		private readonly LoggerManager $loggerManager,
		ConfigService $configService,
		protected readonly ?OutputInterface $output = null,
	)
	{
		$this->config = ArrayHash::from($configService->get('erp'));
		$this->logToSentry = $this->config->logToSentry;
		$this->logger = $this->getLoggerByImportType($this->loggerManager, $this->type, 'process');
	}

	/**
	 * @phpstan-param  Closure(ImportCache): (void) $itemProcessor
	 */
	public function process(Closure $itemProcessor, int $limit = 0): Result
	{
		$this->logInfo('== START Batch =============================================');

		$result = new Result();
		$items = $this->getNextBatch($limit);

		if ($items->count() === 0) {
			$this->logInfo('No READY items from import');
		}
		$errorCount = [];
		foreach ($items as $item) {
			$result->count++;
			try {
				$item->status = ImportCache::STATUS_PROCESSING;
				$this->orm->importCache->persistAndFlush($item);

				$itemProcessor($item);

				$item->status       = ImportCache::STATUS_IMPORTED;
				$item->message      = 'ok';
				$item->importedTime = new DateTimeImmutable();
				$this->orm->importCache->persistAndFlush($item);
			} catch (SkippedException $e) {
				$item->status  = ImportCache::STATUS_SKIPPED;
				$item->message = strlen($e->getMessage()) > 0 ? $e->getMessage() : 'skipped by exception';
				$this->orm->importCache->persistAndFlush($item);
			} catch (WarningException $e) {
				$item->status  = ImportCache::STATUS_WARNING;
				$item->message = strlen($e->getMessage()) > 0 ? $e->getMessage() : 'undefined warning';
				$this->orm->importCache->persistAndFlush($item);
			} catch (InvalidTypeException $e) {
				throw $e; //error in the code, most probably
			} catch (Throwable $e) {
				$this->processItemError($item, $e, 'Processing product ERROR');
				$messageHash = md5($e->getMessage());
				$errorCount[$messageHash] = $errorCount[$messageHash] ?? 0;
				$errorCount[$messageHash]++;
				$result->errors[$messageHash] = [
					'errorCount' => $errorCount[$messageHash],
					'message' => $e->getMessage(),
					'file' => $e->getFile(),
					'line' => $e->getLine(),
				];
			}
		}

		$this->logInfo('==== END Batch =============================================', (array) $result);
		return $result;
	}

	protected function processItemError(ImportCache $item, Throwable $e, string $title): void
	{
		$item->status = ImportCache::STATUS_ERROR;
		$item->message = $e->getMessage();
		$this->orm->importCache->persist($item);

		$msg = sprintf('%s | Item ID: %s | %s', $title, $item->id, $e->getMessage());

//		$this->errorLogger->error($msg, [
//			'item' => $item->toArray(ToArrayConverter::RELATIONSHIP_AS_ID),
//			'exception' => [
//				'fle' => $e->getFile(),
//				'line' => $e->getLine(),
//			]
//		]);

		$this->saveBlueScreen($e);

		if ($this->logToSentry) {
			$this->sentryLogger->log($msg, ILogger::ERROR);
			$this->logToSentry = false;
		}
	}

	protected function logInfo(string $message, array $context = []): void
	{
		$this->logger->info($message, $context);
	}

	/**
	 * @return ICollection<ImportCache>
	 * @noinspection PhpDocSignatureInspection
	 */
	public function getNextBatch(int $limit): ICollection
	{
		return $this->orm->importCache->findBy(['type' => $this->type, 'status' => ImportCache::STATUS_READY])
			->orderBy('id')
			->limitBy($limit);
	}

	public function getLogger(): LoggerInterface
	{
		return $this->logger;
	}

	public function saveBlueScreen(Throwable $e): void
	{
		$blueScreenPath = APP_DIR . '/../nettelog/erp/blue-screens';
		FileSystem::createDir($blueScreenPath);
		$logger         = new Logger($blueScreenPath);
		$blueScreenName = $logger->getExceptionFile($e);

		if ( ! file_exists($blueScreenName)) {
			Debugger::getBlueScreen()->renderToFile($e, $blueScreenName);
		}
		if ($this->output !== null) {
			$this->output->writeln('Error: ' . $e->getMessage());
			$this->output->writeln('BlueScreen: ' . $blueScreenName);
		}
	}

}
