<?php

declare(strict_types=1);

namespace App\Model\Erp\Connector;

use InvalidArgumentException;
use function array_values;
use function sprintf;

final class ConnectorRegistry
{

	/** @var array<string, ReadCollection> */
	private array $connectors = [];

	/**
	 * @param ReadCollection[] $connectors
	 */
	public function __construct(
		array $connectors,
	)
	{
		foreach ($connectors as $connector) {
			$this->connectors[$connector::class] = $connector;
		}
	}

	/**
	 * @return list<ReadCollection>
	 */
	public function list(): array
	{
		return array_values($this->connectors);
	}

	/**
	 * @return ReadCollection[]
	 */
	public function toArray(): array
	{
		return $this->connectors;
	}

	public function get(string $connectorName): ReadCollection
	{
		return $this->connectors[$connectorName] ?? throw new InvalidArgumentException(sprintf('Connector with name "%s" not found.', $connectorName));
	}

}
