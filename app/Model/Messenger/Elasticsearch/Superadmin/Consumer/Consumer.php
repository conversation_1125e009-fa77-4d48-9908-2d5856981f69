<?php declare(strict_types = 1);

namespace App\Model\Messenger\Elasticsearch\Superadmin\Consumer;

use App\Model\Orm\Orm;
use App\Model\Orm\Product\Product;
use LogicException;

abstract class Consumer
{

	public function __construct(
		private readonly Orm $orm,
	)
	{
	}

	protected function getObjectByClass(string $class, int $objectId): ?object
	{
		//$this->orm->setPublicOnly(false);

		return match ($class) {
			Product::class => $this->orm->getRepositoryForEntity($class)->getById($objectId),
			default => throw new LogicException(sprintf("Missing definition for '%s' class", $class))
		};
	}

}
