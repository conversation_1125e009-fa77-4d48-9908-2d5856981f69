<?php declare(strict_types = 1);

namespace App\Model\Messenger\Elasticsearch\Superadmin\Consumer;

use App\Model\ElasticSearch\All\ConvertorProvider;
use App\Model\ElasticSearch\All\ElasticAll;
use App\Model\ElasticSearch\Service;
use App\Model\Messenger\Elasticsearch\ConsumerHelper;
use App\Model\Messenger\Elasticsearch\Superadmin\Message\ReplaceSuperadminMessage;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\EsIndex\EsIndexRepository;
use App\Model\Orm\Orm;
use App\Model\Orm\ProductVariant\ProductVariantRepository;
use LogicException;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Throwable;

#[AsMessageHandler]
class ReplaceSuperadminConsumer extends Consumer
{

	public function __construct(
		private readonly EsIndexRepository $esIndexRepository,
		private readonly Service $elasticService,
		private readonly ConvertorProvider $convertorProvider,
		private readonly ConsumerHelper $consumerHelper,
		ProductVariantRepository $productVariantRepository,
		private readonly MutationHolder $mutationHolder,
		private readonly Orm $orm,
		private readonly LoggerInterface $logger,
	)
	{
		parent::__construct(
			$productVariantRepository,
		);
	}

	public function __invoke(ReplaceSuperadminMessage $message): void
	{
		$this->orm->reconnect();
		$esIndex = $this->esIndexRepository->getById($message->getEsIndexId());

		if ($esIndex === null) {
			// Log the missing EsIndex and skip processing instead of throwing an exception
			// This can happen when EsIndex records are cleaned up between message creation and processing
			$this->logger->warning(
				'EsIndex not found during ReplaceSuperadminMessage processing - skipping message',
				[
					'esIndexId' => $message->getEsIndexId(),
					'objectClass' => $message->getClass(),
					'objectId' => $message->getId(),
					'convertors' => $message->getConvertors(),
					'signals' => $message->getSignals(),
				]
			);
			return;
		}

		try {
			$this->orm->setMutation($esIndex->mutation);
			$this->mutationHolder->setMutation($esIndex->mutation);

			$object = $this->getObjectByClass($message->getClass(), $message->getId());
			$convertors = array_map(function ($convertorClass) {
				return $this->convertorProvider->get($convertorClass);
			}, $message->getConvertors());

			$this->elasticService->replaceDoc($esIndex, new ElasticAll($object, $convertors));

			if (($signals = $message->getSignals()) !== []) {
				$this->consumerHelper->handleSignals($esIndex, $signals);
			}
		} catch (Throwable $e) {
			$this->consumerHelper->handleError($esIndex, $e, $message->getId(), $message->getClass());
		}
	}

}
