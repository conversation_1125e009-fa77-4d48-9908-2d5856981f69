<?php declare(strict_types = 1);

namespace App\Model\Messenger\Elasticsearch\Superadmin\Message;

use App\Model\Orm\EsIndex\EsIndex;

class DeleteSuperadminMessage
{

	private int $esIndexId;

	public function __construct(
		private string $class,
		private int $id,
		EsIndex $esIndex,
	)
	{
		$this->esIndexId = $esIndex->id;
	}

	public function getEsIndexId(): int
	{
		return $this->esIndexId;
	}

	public function getId(): int
	{
		return $this->id;
	}

	public function getClass(): string
	{
		return $this->class;
	}

}
