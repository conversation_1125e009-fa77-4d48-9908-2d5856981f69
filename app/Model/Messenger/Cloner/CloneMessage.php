<?php declare(strict_types = 1);

namespace App\Model\Messenger\Cloner;

use App\Model\Orm\Mutation\Mutation;

class CloneMessage
{

	private int $mutationId;

	public function __construct(
		private readonly string $class,
		private readonly int $id,
		Mutation $mutation,
	)
	{
		$this->mutationId = $mutation->id;
	}


	public function getMutationId(): int
	{
		return $this->mutationId;
	}

	public function getId(): int
	{
		return $this->id;
	}

	public function getClass(): string
	{
		return $this->class;
	}

}
