<?php
declare(strict_types=1);
namespace App\Model;

use App\Model\ElasticSearch\Product\Repository;
use App\Model\ElasticSearch\Product\ResultReader;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Product\Product;
use Nette\Http\Request;
use Nette\Http\Response;
use Nette\Utils\Json;
use Nextras\Orm\Collection\ICollection;
use Throwable;

final class LastVisitedProduct
{
	const COOKIE_NAME = 'lastVisitedProduct';
	const COOKIE_EXPIRE = '+3 months';

	private ?array $list = null;

	private int $maxCount = 100;

	public function __construct(
		private readonly Request $httpRequest,
		private readonly Response $httpResponse,
		private readonly Repository $esRepository,
		private readonly ResultReader $resultReader,
		private readonly MutationHolder $mutationHolder,
		ConfigService $configService,
	)
	{
		$this->maxCount = $configService->getParam('shop', 'lastVisitedProductCount');
	}

	public function add(Product $product): void
	{
		if($product->id) {
			$this->list = $this->getList();
			if (isset($this->list[$product->id])) {
				unset($this->list[$product->id]);
			}
			$this->list[$product->id] = $product->id;
			$this->save();
		}
	}

	/**
	 * Retrieves a sorted list of product IDs from the current list. This array contains all products
	 *
	 * This method obtains a list of product IDs, sorts it in reverse order,
	 * and returns the sorted list.
	 *
	 * @return array An array of product IDs sorted in descending order.
	 */
	public function getProductIds(): array
	{
		$list = array_values($this->getList());
		krsort($list);
		return array_values($list);
	}

	/**
	 * Retrieves a collection of products based on their IDs, considering mutation and maximum count constraints.
	 *
	 * This method interacts with the repository to find products by their IDs. It uses mutation rules
	 * and limits the results to a specified maximum count before mapping them to an entity collection.
	 *
	 * @return ICollection<Product> A collection of product entities derived from the search results.
	 */
	public function getProductCollection(): ICollection
	{
		$resultSet = $this->esRepository->findByIds($this->getProductIds(), $this->mutationHolder->getMutation(), false, $this->maxCount);
		return $this->resultReader->mapResultToEntityCollection($resultSet, $this->getProductIds());
	}

	private function getList(): array
	{
		if ($this->list === null) {
			$visitedCookie = $this->loadCookie();
			$this->list = [];
			foreach ($visitedCookie as $productId) {
				$this->list[$productId] = $productId;
			}
		}

		// keep +1 product in cookie
		if (count($this->list) > $this->maxCount) {
			$this->list = array_slice($this->list, count($this->list) - $this->maxCount, NULL, TRUE);
		}

		return $this->list;
	}

	private function loadCookie(): array
	{
		try {
			$value = $this->httpRequest->getCookie(self::COOKIE_NAME);

			if($value !== null){
				return Json::decode($value, forceArrays: true);
			}

		} catch (Throwable $e) {

		}
		return [];
	}


	private function save(): void
	{
		$this->httpResponse->setCookie(self::COOKIE_NAME, Json::encode($this->list), self::COOKIE_EXPIRE);
	}
}
