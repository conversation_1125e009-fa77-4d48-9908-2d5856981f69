<?php

declare(strict_types=1);

namespace App\Model\TagManager\Bloomreach\Tag;

use App\Event\ProductDetailView;
use App\Model\Currency\CurrencyHelper;
use App\Model\Security\User;
use App\Model\TagManager\RenderableTag;
use App\Model\TagManager\Renderer\BloomreachRenderer;

final readonly class ViewItem implements RenderableTag
{

	public function __construct(
		private ProductDetailView $productDetailView,
		private User $user,
	)
	{
	}

	public function getEventName(): string
	{
		return 'view_item';
	}

	public function getData(): array
	{
		return $this->productDetailView->productLocalization->getBloomreachData(
			$this->productDetailView->productVariant,
			$this->productDetailView->mutation,
			$this->productDetailView->priceLevel,
			$this->productDetailView->state,
			$this->user
		) + [
				'local_currency' => CurrencyHelper::getCurrencyCode(),
				'language' => $this->productDetailView->mutation->langCode,
				'locale' => $this->productDetailView->mutation->isoCode,
				'timestamp' => time(),
			];
	}

	public function getRendererName(): string
	{
		return BloomreachRenderer::class;
	}

	public function getDomElementId(): ?string
	{
		return null;
	}

}
