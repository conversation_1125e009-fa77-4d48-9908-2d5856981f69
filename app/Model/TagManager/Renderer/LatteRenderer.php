<?php

declare(strict_types=1);

namespace App\Model\TagManager\Renderer;

use App\Model\ConfigService;
use App\Model\TagManager\RenderableTag;
use Latte\Engine;
use Latte\Runtime\Html;
use Nette\Bridges\ApplicationLatte\LatteFactory;

abstract class LatteRenderer implements Renderer
{

	protected Engine $latte;

	public function __construct(
		protected readonly ConfigService $configService,
		LatteFactory $latteFactory,
		protected readonly bool $debugMode = false,
	)
	{
		$this->latte = $latteFactory->create();
	}

	public function render(RenderableTag $tag): ?Html
	{
		if ($this->checkRequirements() === false) {
			return null;
		}
		return new Html($this->latte->renderToString(__DIR__ . '/templates/' . $this->getTemplateName() . '.latte', ['tag' => $tag, 'debug' => $this->debugMode]));
	}

	protected function checkRequirements(): bool
	{
		return true;
	}

	abstract public function getTemplateName(): string;

}
