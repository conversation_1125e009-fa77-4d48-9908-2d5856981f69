<?php

declare(strict_types=1);

namespace App\Model\TagManager\GTM\Tag;

use App\Event\PageView;
use App\FrontModule\Presenters\Catalog\CatalogPresenter;
use App\FrontModule\Presenters\Error\ErrorPresenter;
use App\FrontModule\Presenters\Homepage\HomepagePresenter;
use App\FrontModule\Presenters\Order\OrderPresenter;
use App\FrontModule\Presenters\Product\ProductPresenter;
use App\FrontModule\Presenters\Search\SearchPresenter;
use App\Model\TagManager\RenderableTag;
use App\Model\TagManager\Renderer\DataLayerPushRenderer;
use App\PostType\Discount\FrontModule\Presenters\DiscountPresenter;
use App\PostType\ProductReview\FrontModule\Presenters\ProductReviewPresenter;

final readonly class GTMPageView implements RenderableTag
{

	public function __construct(
		private PageView $pageViewEvent
	)
	{
	}

	public function getRendererName(): string
	{
		return DataLayerPushRenderer::class;
	}

	public function getEventName(): string
	{
		return 'page_view';
	}

	private function getPageType(): string
	{
		$presenter = $this->pageViewEvent->presenter;
		if ($presenter instanceof HomepagePresenter) {
			return 'homepage';
		} elseif ($presenter instanceof CatalogPresenter) {
			return 'category';
		} elseif ($presenter instanceof SearchPresenter) {
			return 'search';
		} elseif ($presenter instanceof OrderPresenter) {
			return ($presenter->getAction() === 'default') ? 'cart' : 'checkout';
		} elseif ($presenter instanceof DiscountPresenter) {
			return 'promotions';
		} elseif ($presenter instanceof ErrorPresenter) {
			return 'error';
		} elseif ($presenter instanceof ProductReviewPresenter) {
			return 'reviews';
		} elseif ($presenter instanceof ProductPresenter) {
			return 'product_detail';
		}

		return 'static';
	}

	public function getData(): array
	{
		$loggedIn = $this->pageViewEvent->user !== null;
		$user = ['loggedIn' => $loggedIn];
		if ($loggedIn) {
			$user['userId'] = (string) $this->pageViewEvent->user?->id;
			$user['email'] = $this->pageViewEvent->user?->email;
			$user['phone'] = $this->pageViewEvent->user?->phone;
			$user['accountType'] = $this->pageViewEvent->user->isClubMember ? 'Člen DK' : 'Není člen DK';
		}

		return [
			'event' => $this->getEventName(),
			'user' => $user,
			'page' => [
				'type' => $this->getPageType(),
				'title' => strip_tags($this->pageViewEvent->routableEntity->getNameTitle()),
				'location' => (string) $this->pageViewEvent->httpRequest->getUrl(),
				'referer' => $this->pageViewEvent->httpRequest->getReferer(),
			],
			'_clear' => true,
		];
	}
	public function getDomElementId(): ?string
	{
		return null;
	}

}
