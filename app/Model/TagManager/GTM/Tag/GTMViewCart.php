<?php

declare(strict_types=1);

namespace App\Model\TagManager\GTM\Tag;

use App\Event\ViewCart;
use App\Model\Orm\Order\Product\ProductItem;
use App\Model\Orm\Price;
use App\Model\TagManager\RenderableTag;
use App\Model\TagManager\Renderer\DataLayerPushRenderer;

final readonly class GTMViewCart implements RenderableTag
{

	public function __construct(
		private ViewCart $viewCart
	)
	{
	}

	public function getRendererName(): string
	{
		return DataLayerPushRenderer::class;
	}

	public function getEventName(): string
	{
		return 'view_cart';
	}

	public function getData(): array
	{
		$items = [];

		if ($this->viewCart->shoppingCart->hasOrder()) {
			$mutation   = $this->viewCart->shoppingCart->getMutation();
			$currency   = $this->viewCart->shoppingCart->getCurrency();

			$i = 0;
			/** @var ProductItem $productItem */
			foreach ($this->viewCart->shoppingCart->getProducts() as $productItem) {
				$items[] = $productItem->variant->product->getLocalization($mutation)->getGTMData(
					variant: $productItem->variant,
					currency: $currency,
					index: $i,
					quantity: $productItem->amount,
				);
				$i++;
			}
		}

		return [
			'event' => $this->getEventName(),
			'ecommerce' => [
				'currency' => $this->viewCart->shoppingCart->getCurrency()->getCurrencyCode(),
				'value' => Price::from($this->viewCart->shoppingCart->getTotalPrice(precision: 2))->asMoney()->getAmount()->toFloat(),
				'items' => $items,
			],
			'_clear' => true,
		];
	}
	public function getDomElementId(): ?string
	{
		return null;
	}

}
