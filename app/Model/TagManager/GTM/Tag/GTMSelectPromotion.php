<?php

declare(strict_types=1);

namespace App\Model\TagManager\GTM\Tag;

use App\Event\BannerClicked;
use App\Model\TagManager\RenderableTag;
use App\Model\TagManager\Renderer\DataLayerPushRenderer;

final readonly class GTMSelectPromotion implements RenderableTag
{

	public function __construct(
		private BannerClicked $bannerClicked
	)
	{
	}

	public function getRendererName(): string
	{
		return DataLayerPushRenderer::class;
	}

	public function getEventName(): string
	{
		return 'select_promotion';
	}


	public function getData(): array
	{
		$ecommerce = [
			'creative_name' => $this->bannerClicked->creativeName,
			'creative_slot' => $this->bannerClicked->creativeSlot,
			'promotion_id' => $this->bannerClicked->creativeName,
			'promotion_name' => $this->bannerClicked->creativeName,
		];
		return [
			'event' => $this->getEventName(),
			'ecommerce' => $ecommerce,
			'_clear' => true,
		];
	}
	public function getDomElementId(): ?string
	{
		return null;
	}

}
