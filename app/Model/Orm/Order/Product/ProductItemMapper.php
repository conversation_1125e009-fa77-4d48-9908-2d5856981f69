<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Product;

use App\Model\Orm\Order\OrderState;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<ProductItem>
 */
final class ProductItemMapper extends DbalMapper
{

	use HasCamelCase;

	/**
	 * @return literal-string
	 */
public function getTableName(): string
{
	return 'order_product';
}

	public function getSalesDataFor6Month(Product $product): int
	{
		$variantIds = $product->variants->toCollection()->fetchPairs(null, 'id');

		if ($variantIds === []) {
			return 0;
		}

		$result = $this->connection->query('SELECT COUNT(*) from `order_product` AS op
										LEFT JOIN `order` AS o ON(op.orderId = o.id)
										WHERE variantId IN %i[] AND o.state != %s', $variantIds, OrderState::Canceled);
		return (int) $result->fetchField(0);
	}

}
