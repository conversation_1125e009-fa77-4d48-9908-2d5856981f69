<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Class;

use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Orm\Mapper\Dbal\DbalMapper;
use Nextras\Orm\Collection\ICollection;
use App\Model\Orm\User\User;
use Nextras\Dbal\QueryBuilder\QueryBuilder;
use App\Model\Orm\Order\Payment\PaymentState;

/**
 * @extends DbalMapper<ClassItem>
 */
final class ClassItemMapper extends DbalMapper
{

	use HasCamelCase;

	/**
	 * @return literal-string
	 */
	public function getTableName(): string
	{
		return 'order_class';
	}

	private function prepareClassesQuery(?User $user, ?String $orderHash, bool $onlyPayed = true): null|QueryBuilder
	{
		$builder = $this->builder()
			->select('oc.*')
			->from('order_class', 'oc')
			->joinInner('[order] as o', '[o.id] = [oc.orderId]')
			->orderBy('oc.classEventDateFrom', ICollection::DESC)
			->groupBy('oc.id');

		if ($user) {
			$builder->andWhere('o.userId = %i', $user->id);
		} elseif ($orderHash) {
			$builder->andWhere('o.hash = %s', $orderHash);
		} else {
			return null;
		}

		if ($onlyPayed) {
			$builder
				->joinInner('[order_payment] as op', '[op.id] = [o.paymentId]')
				->joinInner('[order_payment_information] as pi', '[pi.id] = [op.informationId]')
				->andWhere('pi.state = %s', PaymentState::Completed);
		}

		return $builder;
	}

	/**
	 * @return ICollection<ClassItem>
	 */
	public function getPlannedClasses(?User $user = null, ?String $orderHash = null): ICollection
	{
		$query = $this->prepareClassesQuery($user, $orderHash)
			->andWhere('oc.classEventDateFrom >= %dt', new \DateTime());
		return $this->toCollection($query);
	}

	/**
	 * @return ICollection<ClassItem>
	 */
	public function getPastClasses(?User $user = null, ?String $orderHash = null): ICollection
	{
		$query = $this->prepareClassesQuery($user, $orderHash)
			->andWhere('oc.classEventDateFrom < %dt OR oc.classEventDateFrom IS NULL', new \DateTime());
		return $this->toCollection($query);
	}

	/**
	 * @return ICollection<ClassItem>
	 */
	public function getAllClasses(?User $user = null, ?String $orderHash = null): ICollection
	{
		$query = $this->prepareClassesQuery($user, $orderHash);
		return $this->toCollection($query);
	}

}
