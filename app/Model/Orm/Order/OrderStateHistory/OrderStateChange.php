<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\OrderStateHistory;

use App\Model\Orm\BackedEnumWrapper; // phpcs:ignore
use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\OrderState;
use DateTimeImmutable;
use Nextras\Orm\Entity\Entity;

/**
 * @property-read int $id {primary}
 * @property-read Order $order {m:1 Order::$stateChanges}
 *
 * @property-read DateTimeImmutable $changedAt
 * @property-read OrderState|null $from {wrapper BackedEnumWrapper}
 * @property-read OrderState $to {wrapper BackedEnumWrapper}
 */
final class OrderStateChange extends Entity
{

	public static function of(
		Order $order,
		OrderState|null $from,
		OrderState $to,
	): OrderStateChange
	{
		$self = new self();
		$self->setReadOnlyValue('order', $order);
		$self->setReadOnlyValue('changedAt', new DateTimeImmutable());
		$self->setReadOnlyValue('from', $from);
		$self->setReadOnlyValue('to', $to);
		return $self;
	}

}
