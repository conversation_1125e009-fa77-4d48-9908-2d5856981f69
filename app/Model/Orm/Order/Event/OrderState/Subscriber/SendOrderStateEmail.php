<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Event\OrderState\Subscriber;

use App\Model\Email\CommonFactory;
use App\Model\Orm\Order\Event\OrderState\CancelOrder;
use App\Model\Orm\Order\Event\OrderState\ChangeOrderState;
use App\Model\Orm\Order\Event\OrderState\DeclineOrder;
use Nette\InvalidStateException;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Throwable;
use Tracy\Debugger;
use Tracy\ILogger;

final readonly class SendOrderStateEmail implements EventSubscriberInterface
{

	public function __construct(
		private CommonFactory $mailFactory // @phpstan-ignore-line
	)
	{
	}

	public function __invoke(ChangeOrderState $changeOrderState): void
	{
		//$order = $changeOrderState->getOrder();

		//$mailer = $this->mailFactory->create();

		try {
			//$mailer->send(null, $order->email, 'order_state', ['order' => $order]);
			// TODO: send email about state change
			throw new InvalidStateException('TODO: send email about state change');
		} catch (Throwable $e) {
			Debugger::log($e, ILogger::EXCEPTION);
		}
	}


	public static function getSubscribedEvents(): array
	{
		return [
			CancelOrder::class => [
				['__invoke', 10],
			],
			DeclineOrder::class => [
				['__invoke', 11],
			],
		];
	}

}
