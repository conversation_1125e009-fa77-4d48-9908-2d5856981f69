<?php

declare(strict_types=1);

namespace App\Model\Orm\Usp;

use App\Model\Orm\CollectionById;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @extends Repository<Usp>
 */
final class UspRepository extends Repository implements CollectionById
{

	public static function getEntityClassNames(): array
	{
		return [Usp::class];
	}
	public function getByExtId(int|string $id): ?Usp
	{
		return $this->getBy(['extId' => (string) $id]);
	}

	public function getMapper(): UspMapper
	{
		$mapper = parent::getMapper();
		assert($mapper instanceof UspMapper);
		return $mapper;
	}

	/**
	 * @return ICollection<Usp>
	 */
	public function findByIdOrder(array $ids): ICollection
	{
		return $this->getMapper()->findByIdOrder($ids);
	}
}
