<?php

declare(strict_types=1);

namespace App\Model\Orm\Usp;

use App\Model\Orm\JsonArrayContainer; // phpcs:ignore
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Synchronizable;
use Nextras\Orm\Entity\Entity;


/**
 * @property int $id {primary}
 * @property string|null $extIdRaw {default null}
 * @property string|null $extId {default null}
 * @property string $name
 *
 * RELATIONS
 * @property Mutation $mutation {m:1 Mutation, oneSided=true}
 */
class Usp extends Entity implements Synchronizable
{

	public function getExternalId(): null|string
	{
		return $this->extId;
	}

	public function setExternalId(null|string|int $externalId): void
	{
		$this->extId = (string) $externalId;
	}


}
