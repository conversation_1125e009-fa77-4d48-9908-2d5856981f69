<?php declare(strict_types=1);

namespace App\Model\Orm\State;

use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;
use Nextras\Orm\Mapper\Mapper;

/**
 * @extends DbalMapper<State>
 */
final class StateMapper extends DbalMapper
{
	use HasCamelCase;

	/**
* @return literal-string
*/
public function getTableName(): string
{
	return 'state';
}
	/**
	 * @return ICollection<State>
	 */
	public function searchByName(string $q, array $excluded = []): ICollection
	{
		$builder = $this->builder()
			->andWhere('name LIKE %_like_', $q);

		if ($excluded !== []) {
			$builder->andWhere('id not in %i[]', $excluded);
		}

		return $this->toCollection($builder);
	}
	/**
	 * @return ICollection<State>
	 */
	public function findByIdOrder(array $ids): ICollection
	{
		$builder = $this->builder()
			->andWhere('id in %i[]', $ids)
			->orderBy('%raw', 'FIELD(id, ' . implode(',', $ids) . ')');

		return $this->toCollection($builder);
	}

}
