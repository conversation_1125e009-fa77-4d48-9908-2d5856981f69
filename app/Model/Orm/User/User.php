<?php declare(strict_types = 1);

namespace App\Model\Orm\User;

use App\Model\Orm\ClassEvent\ClassEvent;
use App\Model\Orm\DeliveryMethod\DeliveryMethodConfiguration;
use App\Model\Orm\DeliveryMethod\DeliveryMethodConfigurationRepository;
use App\Model\Orm\JsonContainer; // phpcs:ignore
use App\Model\Orm\BigDecimalContainer; // phpcs:ignore
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\MyLibrary\MyLibrary;
use App\Model\Orm\MyLibrary\MyLibraryModel;
use App\Model\Orm\MyLibrary\MyLibraryRepository;
use App\Model\Orm\NewsletterEmail\NewsletterEmailRepository;
use App\Model\Orm\Order\Order;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\ProductReview\ProductReview;
use App\Model\Orm\State\State;
use App\Model\Orm\Synchronizable;
use App\Model\Orm\Traits\HasConfigService;
use App\Model\Orm\Traits\HasConsts;
use App\Model\Orm\Traits\HasCustomFields;
use App\Model\Orm\Traits\HasTranslator;
use App\Model\Orm\UserHash\UserHash;
use App\Model\Orm\UserMutation\UserMutation;
use App\PostType\Supplier\Model\Orm\Supplier\Supplier;
use App\PostType\SystemMessage\Model\Orm\SystemMessage;
use App\PostType\UserAnimal\Model\Orm\UserAnimal;
use Brick\Math\BigDecimal;
use Nette\Utils\ArrayHash;
use Nette\Utils\Strings;
use Nette\Utils\Validators;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\OneHasMany;

use function json_decode;
use function json_encode;
use const JSON_THROW_ON_ERROR;
use App\Model\Orm\Order\Class\ClassItem;
use App\Model\Orm\Order\Class\ClassItemRepository;
use Nextras\Orm\Collection\ICollection;
use stdClass;

/**
 * @property int $id {primary}
 * @property string|null $extId {default null}
 * @property string $email
 * @property string|null $password
 * @property string|null $googleId
 * @property string|null $facebookId
 * @property string|null $seznamId
 * @property string|null $role {default self::ROLE_USER} {enum self::ROLE_*}
 * @property string|null $firstname {default ''}
 * @property string|null $lastname {default ''}
 * @property string|null $phone {default ''}
 * @property string $street {default ''}
 * @property string $zip {default ''}
 * @property string $city {default ''}
 * @property string $ic {default ''}
 * @property string $dic {default ''}
 * @property string $company {default ''}
 * @property DateTimeImmutable|null $createdTime {default now}
 * @property DateTimeImmutable|null $editedTime
 * @property DateTimeImmutable|null $lastLogin
 * @property string|null $customAddressJson
 * @property string|null $interestsJson
 * @property DateTimeImmutable|null $interestsUpdated {default null}
 * @property string|null $preferredCurrency {default null}
 * @property int $orderCount {default 0}
 * @property bool $freeTransit
 * @property bool $payWithInvoice
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 * @property bool $isActive {default false}
 * @property DateTimeImmutable|null $activatedTime
 *
 * RELATIONS
 * @property OneHasMany<ProductReview> $reviews {1:m ProductReview::$user}
 * @property OneHasMany<UserHash> $hashes {1:m UserHash::$user}
 * @property ManyHasMany<Mutation> $mutations {m:m Mutation::$users, isMain=true}
 * @property PriceLevel $priceLevel {m:1 PriceLevel::$users}
 * @property State|null $state {m:1 State::$users}
 * @property OneHasMany<UserMutation> $userMutations {1:m UserMutation::$user, cascade=[persist, remove]}
 * @property OneHasMany<SystemMessage> $systemMessages {1:m SystemMessage::$editedBy}
 * @property OneHasMany<Supplier> $suppliers {1:m Supplier::$editedBy}
 * @property OneHasMany<Order> $orders {1:m Order::$user}
 * @property ManyHasMany<ClassEvent> $classEvents {m:m ClassEvent::$lectors}
 * @property OneHasMany<UserAnimal> $animals {1:m UserAnimal::$user}
 *
 *
 * VIRTUAL
 * @property-read string $name {virtual}
 * @property-read Mutation|null $mutation {virtual}
 * @property array|null $customAddress {virtual}
 * @property array|null $interests {virtual}
 * @property ArrayHash|null $cf {virtual}
 * @property bool $hasNewsletter {virtual}
 * @property bool $hasPhone {virtual}
 * @property DeliveryMethodConfiguration|null $prefferedDelivery {virtual}
 * @property bool $isClubMember {default false}
 * @property MyLibrary|null $library {virtual}
 */
class User extends Entity implements Synchronizable
{

	use HasConsts;
	use HasConfigService;
	use HasTranslator;
	use HasCustomFields;

	public const ROLE_USER = 'user';
	public const ROLE_ADMIN = 'admin';
	public const ROLE_DEVELOPER = 'developer';
	public const ROLE_LECTOR = 'lector';

	private NewsletterEmailRepository $newsletterEmailRepository;

	private DeliveryMethodConfigurationRepository $deliveryMethodConfigurationRepository;

	private MyLibraryRepository $myLibraryRepository;

	private MyLibraryModel $myLibraryModel;
	private ClassItemRepository $classItemRepository;

	public function injectService(
		NewsletterEmailRepository $newsletterEmailRepository,
		DeliveryMethodConfigurationRepository $deliveryMethodConfigurationRepository,
		MyLibraryRepository $myLibraryRepository,
		MyLibraryModel $myLibraryModel,
		ClassItemRepository $classItemRepository
	): void
	{
		$this->newsletterEmailRepository = $newsletterEmailRepository;
		$this->deliveryMethodConfigurationRepository = $deliveryMethodConfigurationRepository;
		$this->myLibraryRepository = $myLibraryRepository;
		$this->myLibraryModel = $myLibraryModel;
		$this->classItemRepository = $classItemRepository;
	}

	protected function getterLibrary(): MyLibrary|null
	{
		return $this->loadCache($this->createCacheKey('userLibrary', $this), function () {
			if ($library = $this->myLibraryRepository->getBy(['user' => $this->id])) {
				return $library;
			}

			return $this->myLibraryModel->create($this);
		});
	}

	protected function getterCustomAddress(): mixed
	{
		assert($this->getMetadata()->hasProperty('customAddressJson'));
		if ($this->customAddressJson === null) {
			return null;
		}

		return json_decode($this->customAddressJson, flags: JSON_THROW_ON_ERROR);
	}


	protected function setterCustomAddress(mixed $customAddress): void
	{
		assert($this->getMetadata()->hasProperty('customAddressJson'));
		$this->customAddressJson = json_encode($customAddress, JSON_THROW_ON_ERROR);
	}

	protected function getterInterests(): mixed
	{
		assert($this->getMetadata()->hasProperty('interestsJson'));
		if ($this->interestsJson === null) {
			return null;
		}

		return json_decode($this->interestsJson, flags: JSON_THROW_ON_ERROR);
	}


	protected function setterInterests(mixed $interests): void
	{
		assert($this->getMetadata()->hasProperty('interestsJson'));
		if ($interests !== null) {
			$this->interestsJson = json_encode($interests, JSON_THROW_ON_ERROR);
		} else {
			$this->interestsJson = null;
		}
	}

	protected function getterMutation(): ?Mutation
	{
		return $this->mutations->toCollection()->fetch();
	}


	protected function getterName(): string
	{
		return trim($this->firstname . ' ' . $this->lastname);
	}

	protected function getterHasNewsletter(): bool
	{
		if (!isset($this->cache['hasNewsLetter'])) {
			if (Strings::length($this->email) === 0) {
				return false;
			}

			$this->cache['hasNewsLetter'] = $this->newsletterEmailRepository->getBy(['email' => $this->email]) !== null;
		}

		return $this->cache['hasNewsLetter'];
	}

	protected function getterHasPhone(): bool
	{
		if ($this->customAddress === null){
			return false;
		}

		foreach ($this->customAddress as $customAddress){

			if ($customAddress->invPhone !== null){
				return true;
			}

			if ($customAddress->delPhone !== null){
				return true;
			}
		}

		return false;
	}

	protected function getterIsClubMember(): bool
	{
		return $this->loadCache($this->createCacheKey('isClubMember'), function () {
			if (!$this->hasNewsletter) {
				return false;
			}

			if (!$this->hasPhone) {
				return false;
			}

			if (!Validators::isEmail($this->email)) {
				return false;
			}

			return true;
		});
	}

	public function isDeveloper(): bool
	{
		return ($this->role === self::ROLE_DEVELOPER);
	}

	protected function getterPrefferedDelivery(): DeliveryMethodConfiguration|null
	{
		return $this->deliveryMethodConfigurationRepository->getPrefferedDelivery($this);
	}

	public function getExternalId(): ?int
	{
		if ($this->extId === null) {
			return null;
		}
		return (int) $this->extId;
	}

	public function setExternalId(?string $externalId): void
	{
		$this->extId = $externalId;
	}

	public function createCustomAddressFromUserData(): stdClass
	{
		$address = new stdClass();
		$address->inv_firstname = $this->firstname;
		$address->inv_lastname = $this->lastname;
		$address->inv_phone = $this->phone;
		$address->inv_email = $this->email;
		$address->inv_street = $this->street;
		$address->inv_city = $this->city;
		$address->inv_zip = $this->zip;
		$address->inv_state = $this->state?->id;
		$address->inv_company = $this->company;
		$address->inv_ic = $this->ic;
		$address->inv_dic = $this->dic;

		return $address;
	}

	/**
	 * @return ICollection<ClassItem>
	 */
	public function getPlannedClasses(): ICollection
	{
		return $this->classItemRepository->getPlannedClasses($this);
	}

	/**
	 * @return ICollection<ClassItem>
	 */
	public function getPastClasses(): ICollection
	{
		return $this->classItemRepository->getPastClasses($this);
	}

	/**
	 * @return ICollection<ClassItem>
	 */
	public function getAllClasses(): ICollection
	{
		return $this->classItemRepository->getAllClasses($this);
	}
}
