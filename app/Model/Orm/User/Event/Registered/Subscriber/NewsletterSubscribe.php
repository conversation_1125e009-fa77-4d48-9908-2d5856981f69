<?php

declare(strict_types=1);

namespace App\Model\Orm\User\Event\Registered\Subscriber;


use App\Event\Registered;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\NewsletterEmail\NewsletterEmailModel;
use App\Model\Orm\Orm;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Tracy\Debugger;
use Tracy\ILogger;

final readonly class NewsletterSubscribe implements EventSubscriberInterface {

	public function __construct(
		private Orm $orm,
		private NewsletterEmailModel $newsletterEmailModel,
		private MutationHolder $mutationHolder,
	)
	{
	}

	public function __invoke(Registered $event): void
	{
		try {
			$user = $event->user;
			//if ($user->isNewsletter) {
				$this->newsletterEmailModel->subscribeUser($user, $this->mutationHolder->getMutation());
				$this->orm->flush();
			//}

		} catch (\Throwable $exception) {
			bdump($exception);
			Debugger::log($exception, ILogger::EXCEPTION);
		}
	}
	public static function getSubscribedEvents(): array
	{
		return [
			Registered::class => [
				['__invoke', 20],
			],
		];
	}
}
