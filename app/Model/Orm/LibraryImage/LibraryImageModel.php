<?php declare(strict_types = 1);

namespace App\Model\Orm\LibraryImage;

use App\Model\ConfigService;
use App\Model\Image\Storage\BasicStorage;
use App\Model\Orm\Orm;
use GuzzleHttp\Client;
use Nette\Http\FileUpload;
use Nette\Http\Url;
use Nette\Http\UrlScript;
use Nette\Utils\FileSystem;
use Nette\Utils\Image;
use Nette\Utils\Strings;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Tracy\Debugger;
use Tracy\ILogger;

final class LibraryImageModel
{

	public function __construct(
		private readonly Orm           $orm,
		private readonly ConfigService $configService,
		private readonly BasicStorage  $imageStorage,
	)
	{
	}

	public function addFromFileUpload(FileUpload $file, int $cat, bool $isCopy = false, ?string $sourceImage = null, ?string $md5 = null): LibraryImage
	{
		$name = $file->getUntrustedName();
		$image = $this->addFromTmpFile($file->getTemporaryFile(), $cat, $name, $isCopy, $sourceImage, $md5);
		$this->orm->flush();
		return $image;
	}


	public function addFromTmpFile(string $file, ?int $cat, ?string $name = null, bool $isCopy = false, ?string $sourceImage = null, ?string $md5 = null): LibraryImage
	{
		$fileName = $name ?? basename($file);
		$name = Strings::substring($fileName, 0, Strings::indexOf($fileName, '.', -1));

		$newImage = new LibraryImage();
		$newImage->name = $name;
		$newImage->library = $this->orm->libraryTree->getById($cat);

		$this->orm->libraryImage->persist($newImage);

		$filename = $newImage->id . '-' . $this->sanitize($fileName);
		$pathInfo = pathinfo($filename);
		$filenameWithoutExtension = $pathInfo['filename'];
		$extension = $pathInfo['extension'] ?? '';

		$originalPath = $this->imageStorage->getPathToOriginalImage($filenameWithoutExtension, $extension);

		$originalDir = dirname($originalPath);
		if ( ! file_exists($originalDir)) {
			mkdir($originalDir, permissions: 0755, recursive: true);
		}

		copy($file, $originalPath);
		if (!$isCopy && $file !== ($originalPath)) {
			unlink($file);
		}

		if ($this->configService->get('imageOriginal', 'resize')) {
			// resize Orig Image - to save space on disk
			$NewImageWidth      = $this->configService->get('imageOriginal', 'width'); //New Width of Image
			$NewImageHeight     = $this->configService->get('imageOriginal', 'height'); // New Height of Image
			$Quality        = 95; //Image Quality
			$imagePath = $originalPath;
			$destPath = $originalPath;
			$checkValidImage = @getimagesize($imagePath);
			if (file_exists($imagePath) && $checkValidImage !== false) {
				//Continue only if 2 given parameters are true
				//Image looks valid, resize.
				$this->resizeImage($imagePath, $destPath, $NewImageWidth, $NewImageHeight, $Quality);
			}
		}

		$newImage->filename = $filename;
		$newImage->sourceImage = $sourceImage;
		$newImage->md5 = $md5;
		$newImage->sort = -$newImage->id;

		$this->orm->libraryImage->persist($newImage);

		return $newImage;
	}

	public function addFromUrl(string $url, ?int $cat, ?string $name = null, bool $isCopy = false, ?string $sourceImage = null, ?string $md5 = null, ?string $defaultExtension = 'jpg'): LibraryImage
	{
		$client = new Client();

		$newImage = new LibraryImage();

		try {
			$response = $client->request('GET', $url, ['verify' => false]);

			$urlScript = new UrlScript($url);
			$pathinfo = pathinfo((string) $urlScript->withQuery([]));

			if ( ! isset($pathinfo['extension'])) {
				$pathinfo['basename'] .= '.' . $defaultExtension;
			}

			$newImage->name = $name ?? $pathinfo["filename"];
			$newImage->library = $this->orm->libraryTree->getById($cat);

			$this->orm->libraryImage->persist($newImage);

			$filename = $newImage->id . '-' . $this->sanitize($pathinfo["basename"]);

			$pathInfoStored = pathinfo($filename);

			$filenameWithoutExtension = $pathInfoStored['filename'];
			$extension = $pathInfoStored['extension'] ?? $defaultExtension;

			$originalPath = $this->imageStorage->getPathToOriginalImage($filenameWithoutExtension, $extension);
			$originalDir = dirname($originalPath);

			FileSystem::createDir($originalDir, 0755);
			FileSystem::write($originalPath, $response->getBody()->getContents() );

			// resize if set
			if ($this->configService->get('imageOriginal', 'resize')) {
				// resize Orig Image - to save space on disk
				$NewImageWidth      = $this->configService->get('imageOriginal', 'width'); //New Width of Image
				$NewImageHeight     = $this->configService->get('imageOriginal', 'height'); // New Height of Image
				$Quality        = 95; //Image Quality
				$imagePath = $originalPath;
				$destPath = $originalPath;
				$checkValidImage = @getimagesize($imagePath);
				if (file_exists($imagePath) && $checkValidImage !== false) {
					//Continue only if 2 given parameters are true
					//Image looks valid, resize.
					$this->resizeImage($imagePath, $destPath, $NewImageWidth, $NewImageHeight, $Quality);
				}
			}

			$newImage->filename = $filename;
			$newImage->sourceImage = $sourceImage;
			$newImage->md5 = $md5;
			$newImage->sort = -$newImage->id;
			$newImage->timeOfChange = new DateTimeImmutable();

			$this->orm->libraryImage->persistAndFlush($newImage);

		} catch (\Throwable $e){
			Debugger::log($e, ILogger::EXCEPTION);
		}

		return $newImage;
	}

	private function sanitize(string $filename): string
	{
		$name = Strings::webalize($filename, '.', false);
		$name = str_replace(['-.', '.-'], '.', $name);
		$name = trim($name, '.-');
		$name = mb_strtolower($name);

		return $name === '' ? 'unknown' : $name;
	}

	// TODO REF presunout - mela by existovat jedna ovecna classa na resize obrazku
	public function resizeImage(string $SrcImage, string $DestImage, int $MaxWidth, int $MaxHeight, int $Quality): bool
	{
		$imageSize = getimagesize($SrcImage);
		\assert($imageSize !== false);

		[$iWidth, $iHeight] = $imageSize;

		if ($iWidth <= $MaxWidth && $iHeight <= $MaxHeight) {
			return false;
		}

		$ImageScale  = min($MaxWidth / $iWidth, $MaxHeight / $iHeight);
		$NewWidth    = ceil($ImageScale * $iWidth);
		$NewHeight   = ceil($ImageScale * $iHeight);

		$image = Image::fromFile($SrcImage);
		$NewWidth = (int) $NewWidth;
		$NewHeight = (int) $NewHeight;
		$image->resize($NewWidth, $NewHeight);
		$image->save($DestImage);
		return true;
	}

    public function deleteImage(LibraryImage $image): void
    {
		$fileInfo = pathinfo($image->filename);

		if (isset($fileInfo['extension'])) {
			$this->imageStorage->deleteOriginal($fileInfo['filename'], $fileInfo['extension']);
			$this->imageStorage->deleteAllSizes($fileInfo['filename'], $fileInfo['extension']);
		}

		$this->orm->libraryImage->removeAndFlush($image);
    }

}
