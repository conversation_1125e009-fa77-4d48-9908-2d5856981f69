<?php

declare(strict_types=1);

namespace App\Model\Orm\ProductVariantLocalization;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;
use Nextras\Dbal\Drivers\Exception\UniqueConstraintViolationException;

final class ProductVariantLocalizationModel
{

	public function __construct(
		private readonly ProductVariantLocalizationRepository $repository,
	) {}


	public function createForAllVariant(Mutation $mutation, Product $product): void
	{
		foreach ($product->variants as $variant) {
			$this->create($mutation, $variant);
		}
	}


	public function deleteForAllVariant(Mutation $mutation, Product $product): void
	{
		foreach ($product->variants as $variant) {
			$this->delete($mutation, $variant);
		}
	}


	public function create(Mutation $mutation, ProductVariant $variant): void
	{
		$productVariantLocalization = new ProductVariantLocalization();
		$productVariantLocalization->mutation = $mutation;

		try {
			$variant->variantLocalizations->add($productVariantLocalization);
			$this->repository->persistAndFlush($productVariantLocalization);
		} catch (UniqueConstraintViolationException $e) {
			// conflict with default mutation
		}
	}


	public function delete(Mutation $mutation, ProductVariant $variant): void
	{
		$productVariantLocalization = $this->repository->getBy([
			'variant' => $variant,
			'mutation' => $mutation,
		]);

		if ($productVariantLocalization) {
			$this->repository->removeAndFlush($productVariantLocalization);
		}
	}

}
