<?php declare(strict_types = 1);

namespace App\Model\Orm\EsIndex;

use App\Model\Orm\Mutation\Mutation;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method EsIndex|null getById($id)
 * @method EsIndex getByIdChecked($id)
 *
 * @extends Repository<EsIndex>
 */
final class EsIndexRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [EsIndex::class];
	}

	public function getLastActive(string $type, ?Mutation $mutation = null): ?EsIndex
	{
		return $this->findBy([
			'mutation' => $mutation,
			'type' => $type,
			'active' => 1,
		])->orderBy('id', ICollection::DESC)->fetch();
	}


	public function getAllLastActive(Mutation $mutation): ?EsIndex
	{
		return $this->getLastActive(EsIndex::TYPE_ALL, $mutation);
	}

	public function getSuperadminLastActive(Mutation $mutation): ?EsIndex
	{
		return $this->getLastActive(EsIndex::TYPE_SUPERADMIN, $mutation);
	}

	public function getCommonLastActive(Mutation $mutation): ?EsIndex
	{
		return $this->getLastActive(EsIndex::TYPE_COMMON, $mutation);
	}


	public function getProductLastActive(Mutation $mutation): ?EsIndex
	{
		return $this->getLastActive(EsIndex::TYPE_PRODUCT, $mutation);
	}

	/**
	 * @return ICollection<EsIndex>
	 */
	public function findActiveByType(string $type): ICollection
	{
		return $this->findBy([
			'active' => 1,
			'type' => $type,
		]);
	}

}
