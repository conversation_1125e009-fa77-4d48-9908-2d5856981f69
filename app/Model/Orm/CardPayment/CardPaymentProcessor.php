<?php

declare(strict_types=1);

namespace App\Model\Orm\CardPayment;

use App\Model\Orm\CardPayment\PaymentGateway\PaymentGateway;
use App\Model\Orm\CardPayment\PaymentGateway\PaymentGatewayRegistry;
use App\Model\Orm\Order\Event\PaymentCompleted\PaymentCompleted;
use App\Model\Orm\Order\Event\PaymentFailed\PaymentFailed;
use App\Model\Orm\Order\Event\PaymentRefunded\PaymentRefunded;
use App\Model\Orm\Order\Event\PaymentUnknown\PaymentUnknown;
use App\Model\Orm\Order\Payment\PaymentState;
use App\Model\Orm\Orm;
use Nextras\Orm\Collection\ICollection;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;

final readonly class CardPaymentProcessor
{

	public function __construct(
		private PaymentGatewayRegistry $registry,
		private EventDispatcherInterface $eventDispatcher,
		private Orm $orm,
	)
	{
	}

	/**
	 * This method is expected to be called from the presenter that handles return/webhook requests from the payment gateway.
	 * It checks the gateway for the payment's current status, updates local entities, and dispatches events accordingly.
	 */
	public function checkPayment(
		string $gatewayIdentifier,
		string $paymentId,
		array $data = [],
	): void
	{
		$payments = $this->orm->cardPayment->findBy([
			'paymentGatewayUniqueIdentifier' => $gatewayIdentifier,
			'externalId' => $paymentId,
		])->orderBy('createTime', ICollection::DESC)->limitBy(1)->fetchAll();
		$payment = $payments[0] ?? null;

		if ($payment === null) {
			return;
		}

		$this->processCheckPayment($payment, $data);
	}

	public function processCheckPayment(CardPayment $payment, array $data = []): void
	{
		$originalStatus = $payment->status;

		$gateway = $this->registry->get($payment->paymentGatewayUniqueIdentifier);
		$gateway->checkPayment($payment, $data);

		$newPaymentState = $payment->status->toPaymentState();

		if ($payment->status !== $originalStatus
			&& $payment->cardPaymentInformation->state !== $newPaymentState
		) {
			$payment->cardPaymentInformation->setReadOnlyValue('state', $newPaymentState);

			match ($newPaymentState) {
				PaymentState::Completed => $this->eventDispatcher->dispatch(new PaymentCompleted($payment->cardPaymentInformation->payment)),
				PaymentState::Failed => $this->eventDispatcher->dispatch(new PaymentFailed($payment->cardPaymentInformation->payment)),
				PaymentState::Refunded => $this->eventDispatcher->dispatch(new PaymentRefunded($payment->cardPaymentInformation->payment)),
				PaymentState::Unknown => $this->eventDispatcher->dispatch(new PaymentUnknown($payment->cardPaymentInformation->payment)),
				PaymentState::Pending => null,
			};
		}
		$this->orm->persistAndFlush($payment);
	}

	public function getGateway(string $paymentGatewayUniqueIdentifier): PaymentGateway
	{
		return $this->registry->get($paymentGatewayUniqueIdentifier);
	}

}
