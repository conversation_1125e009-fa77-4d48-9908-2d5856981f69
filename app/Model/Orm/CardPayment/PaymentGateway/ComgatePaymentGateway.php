<?php

declare(strict_types=1);

namespace App\Model\Orm\CardPayment\PaymentGateway;

use App\Model\ConfigService;
use App\Model\Orm\CardPayment\CardPayment;
use App\Model\Orm\CardPayment\CardPaymentStatus;
use App\Model\Orm\Order\Payment\CardPaymentInformation;
use Comgate\SDK\Client;
use Comgate\SDK\Comgate;
use Comgate\SDK\Entity\Codes\CategoryCode;
use Comgate\SDK\Entity\Codes\DeliveryCode;
use Comgate\SDK\Entity\Codes\PaymentMethodCode;
use Comgate\SDK\Entity\Codes\PaymentStatusCode;
use Comgate\SDK\Entity\Payment;
use Contributte\Monolog\LoggerManager;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;

final class ComgatePaymentGateway implements PaymentGateway
{

	public const ID = 'ComgateGateway';

	private array $config;

	private ?Client $client = null;

	public function __construct(
		ConfigService $configService,
		private readonly LoggerManager $loggerManager,
	)
	{
		$this->config = $configService->get('payments', 'comgate');
	}

	public function getClient(): Client
	{
		if ($this->client === null) {
			$comgate = Comgate::defaults()->setMerchant((string) $this->config['id'])->setSecret($this->config['secret']);

			if ($this->config['logger'] ?? false) {
				$comgate->setLogger($this->loggerManager->get('comgate'));
			}

			$this->client = $comgate->createClient();
		}
		return $this->client;
	}

	public function getUniqueIdentifier(): string
	{
		return self::ID;
	}

	public function createPayment(CardPaymentInformation $cardPaymentInformation): CardPayment
	{
		$order = $cardPaymentInformation->payment->order;
		$totalPrice = $order->getTotalPriceWithDeliveryVat();

		$payment = new Payment();
		$payment->setPrice($totalPrice->getAmount()->toFloat());
		$payment->setCurrency($totalPrice->getCurrency()->getCurrencyCode());
		$payment->setReferenceId($order->orderNumber);
		$payment->setTest($this->config['testMode'] ?? false);
		$payment->setEmail($order->email);
		$payment->setFullName($order->name);
		$payment->setCategory(CategoryCode::PHYSICAL_GOODS_ONLY);
		$payment->setDelivery(DeliveryCode::HOME_DELIVERY);
		$payment->addMethod(PaymentMethodCode::ALL);
		$payment->setLabel('Objednávka ' . $order->orderNumber);
		$payment->setExpirationTime($this->config['expirationTime'] ?? null);

		$createPayment = $this->getClient()->createPayment($payment);

		return new CardPayment(
			cardPaymentInformation: $cardPaymentInformation,
			gateway: $this,
			externalId: $createPayment->getTransId(),
			externalUrl: $createPayment->getRedirect(),
			expireTime: $this->getExpireTime()
		);
	}

	private function getExpireTime(): ?DateTimeImmutable
	{
		if (($expireString = ($this->config['expirationTime'] ?? null)) !== null) {
			$expireSeconds = str_replace(['m', 'h', 'd'], [' minute', ' hour', ' day'], $expireString);
			return (new DateTimeImmutable())->modify('+' . $expireSeconds);
		}
		return null;
	}

	public function checkPayment(CardPayment $payment, array $data = []): void
	{
		$paymentStatusResponse = $this->getClient()->getStatus($payment->externalId);
		switch ($paymentStatusResponse->getStatus()) {
			case PaymentStatusCode::PAID:
				$payment->updateStatus(CardPaymentStatus::Settled);
				break;

			case PaymentStatusCode::CANCELLED:
				$payment->updateStatus(CardPaymentStatus::Canceled);
				break;

			case PaymentStatusCode::AUTHORIZED:
				$payment->updateStatus(CardPaymentStatus::Authorized);
				break;
		}
		$payment->response = ArrayHash::from($paymentStatusResponse->toArray());
		$payment->checkTime = new DateTimeImmutable();
	}

	public function refundPayment(CardPayment $payment): void
	{
		// TODO: Implement refundPayment() method.
	}

}
