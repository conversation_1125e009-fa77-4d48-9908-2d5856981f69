<?php

declare(strict_types=1);

namespace App\Model\Orm\File;

use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<File>
 */
final class FileMapper extends DbalMapper
{
	use HasCamelCase;
	/**
* @return literal-string
*/
public function getTableName(): string
{
	return 'file';
}

	/**
	 * @return ICollection<File>
	 */
	public function findByExactOrder(array $ids) : ICollection
	{
		$builder = $this->builder()->select('f.*')->from('file', 'f')
			->andWhere('f.id in %i[]', $ids)
			->orderBy('%raw', 'FIELD(f.id, '.implode(',', $ids).')');
		return  $this->toCollection($builder);
	}

}
