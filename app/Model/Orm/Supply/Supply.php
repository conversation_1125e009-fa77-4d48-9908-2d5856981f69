<?php

declare(strict_types=1);

namespace App\Model\Orm\Supply;

use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\Stock\Stock;
use App\Model\Orm\Traits\HasTemplateCache;
use App\PostType\Supplier\Model\Orm\Supplier\Supplier;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;

/**
 * @property int $id {primary}
 * @property int $amount {default 0}
 * @property DateTimeImmutable|null $lastImport
 * @property DateTimeImmutable|null $lastOnStock {default null}
 * @property DateTimeImmutable|null $stockDate {default null}
 * @property int $deliveryDelay {default 0}
 *
 *
 * RELATIONS
 * @property Stock $stock {m:1 Stock::$supplies}
 * @property ProductVariant $variant {m:1 ProductVariant::$supplies}
 * @property Supplier|null             $supplier    {m:1 Supplier, oneSided=true}
 *
 *
 * VIRTUALS
 */
class Supply extends Entity
{
	use HasTemplateCache;

	public function getTemplateCacheTagsCascade(): array
	{
		return [ProductVariant::class.'/'.$this->variant->id];
	}
}
