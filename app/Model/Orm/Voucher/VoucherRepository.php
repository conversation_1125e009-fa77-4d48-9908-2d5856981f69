<?php

declare(strict_types=1);

namespace App\Model\Orm\Voucher;

use App\Model\Orm\Traits\HasPublicParameter;
use Nextras\Orm\Repository\Repository;

/**
 * @extends Repository<Voucher>
 */
final class VoucherRepository extends Repository
{
	use HasPublicParameter;
	public static function getEntityClassNames(): array
	{
		return [Voucher::class];
	}

	public function getPublicOnlyWhereParams(): array
	{
		$ret = [
			'public' => 1,
		];

		$now = $this->getNowDateTime();

		$ret['publicFrom<='] = $now;
		$ret['publicTo>='] = $now;

		return $ret;
	}
}
