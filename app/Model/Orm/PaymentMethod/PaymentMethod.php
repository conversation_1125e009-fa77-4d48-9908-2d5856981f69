<?php

declare(strict_types=1);

namespace App\Model\Orm\PaymentMethod;

use App\Model\Orm\CardPayment\PaymentGateway\PaymentGateway;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\OrderProxy;
use App\Model\Orm\Order\Payment\PaymentType;

interface PaymentMethod
{

	public function getUniqueIdentifier(): string;

	public function getPaymentType(): PaymentType;

	public function isAvailableForOrder(OrderProxy $order): bool;

	public function getPaymentGateway(): ?PaymentGateway;

	public function onOrderPlaced(Order $order): void;

	public function onOrderCanceled(Order $order): void;

	public function getGroup(): ?PaymentMethodGroup;

}
