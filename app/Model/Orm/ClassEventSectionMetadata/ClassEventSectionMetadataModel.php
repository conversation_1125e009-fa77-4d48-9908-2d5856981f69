<?php declare(strict_types = 1);

namespace App\Model\Orm\ClassEventSectionMetadata;

use App\Model\Orm\ClassEvent\ClassEvent;
use App\Model\Orm\ClassSection\ClassSection;

final class ClassEventSectionMetadataModel
{

	public function __construct(
		private readonly ClassEventSectionMetadataRepository $classEventSectionMetadataRepository
	)
	{
	}


	public function create(ClassEvent $classEvent, ClassSection $classSection, string $content, string $description): ClassEventSectionMetadata
	{
		$newClassEventSectionMetadata = new ClassEventSectionMetadata();
		$newClassEventSectionMetadata->classEvent = $classEvent;
		$newClassEventSectionMetadata->classSection = $classSection;
		$newClassEventSectionMetadata->content = $content;
		$newClassEventSectionMetadata->description = $description;
		$this->classEventSectionMetadataRepository->attach($newClassEventSectionMetadata);
		$this->classEventSectionMetadataRepository->persistAndFlush($newClassEventSectionMetadata);

		return $newClassEventSectionMetadata;
	}

}
