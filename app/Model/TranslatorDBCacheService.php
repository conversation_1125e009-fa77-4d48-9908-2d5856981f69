<?php declare(strict_types = 1);

namespace App\Model;

use App\AdminModule\Presenters\String\StringPresenter;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Mutation\Mutation;
use Nette\Caching\Cache;


final class TranslatorDBCacheService
{
	public const CACHE_TAG_PREFIX = 'TR-';
	public const CACHE_TAG_PAGE = 'PAGE_TR';
	public const CACHE_TAG_KEY_PREFIX = 'TR-KEY-';
	private Cache $cache;

	public function __construct(
		private readonly CacheFactory $cacheFactory,
		private readonly MutationsHolder $mutationsHolder,
	)
	{
		$this->init();
	}

	private function init(): void
	{
		$this->cache = $this->cacheFactory->create('translations');
	}

	public function cleanCache(?Mutation $mutation = null): void
	{
		if (!isset($mutation)) {
			foreach ($this->mutationsHolder->findAll(false) as $m) {
				$this->cache->clean([
					Cache::Tags => [self::CACHE_TAG_PREFIX . $m->langCode, self::CACHE_TAG_PAGE],
				]);
			}
		} else {
			$this->cache->clean([
				Cache::Tags => [self::CACHE_TAG_PREFIX . $mutation->langCode, self::CACHE_TAG_PAGE],
			]);
		}
		$this->cache->remove(StringPresenter::STRING_DATASOURCE_CACHE);
	}

	public function cleanCacheByKey(string $name): void
	{
		$this->cache->clean([
			Cache::Tags => [self::CACHE_TAG_KEY_PREFIX . $name, self::CACHE_TAG_PAGE],
		]);
		$this->cache->remove(StringPresenter::STRING_DATASOURCE_CACHE);
	}

	private function getKey(Mutation $mutation, string $sanitizeKey): string
	{
		return 'l' . $mutation->langCode . '_' . $sanitizeKey;
	}

	public function load(Mutation $mutation, string $sanitizeKey): ?string
	{
		return $this->cache->load($this->getKey($mutation, $sanitizeKey));
	}

	public function save(Mutation $mutation, string $sanitizeKey, string $clearTranslationString): void
	{
		$dependencies = [];
		$dependencies[Cache::Tags] = [ self::CACHE_TAG_KEY_PREFIX . $sanitizeKey, self::CACHE_TAG_PREFIX . $mutation->langCode];

		$this->cache->save($this->getKey($mutation, $sanitizeKey),  $clearTranslationString, $dependencies);
	}


}
