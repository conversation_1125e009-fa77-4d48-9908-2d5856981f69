<?php declare(strict_types = 1);

namespace App\Model\CustomField;

use App\Exceptions\LogicException;
use JsonSerializable;
use Nette\Utils\Json;
use Nextras\Orm\Entity\IEntity;
use Nextras\Orm\Repository\IRepository;
use stdClass;
use function get_class;
use function sprintf;


class LazyValue implements JsonSerializable
{

	private IRepository $repository; // @phpstan-ignore-line

	private int $id;

	private ?IEntity $entity = null;

	public string $cName;

	public function __construct(IRepository $repository, int $id) // @phpstan-ignore-line
	{
		$this->repository = $repository;
		$this->id = $id;
	}


	private function init(): void
	{
		if (!$this->entity) {
			$this->entity = $this->repository->getById($this->id);
		}
	}


	public function __get(string $name): mixed
	{
		$this->init();

		if (isset($this->entity->$name)) {
			return $this->entity->$name;
		}

		if (isset($this->$name)) {
			return $this->$name;
		}

		throw new LogicException(sprintf(
			'Property $%s not found in LazyValue(%d) of %s.',
			$name,
			$this->id,
			get_class($this->repository),
		));
	}


	public function __set(string $name, mixed $value): void
	{
		$this->$name = $value;
	}


	public function __isset(string $name): bool
	{
		$this->init();
		return isset($this->$name) || isset($this->entity->$name);
	}


	public function __call(string $name, array $arguments): mixed
	{
		$this->init();
		if ($this->entity && method_exists($this->entity, $name)) {
			return $this->entity->$name(...$arguments);
		}

		throw new LogicException(sprintf(
			'Method %s() not found in LazyValue(%d) of %s.',
			$name,
			$this->id,
			get_class($this->repository),
		));
	}


	public function getEntity(): ?IEntity
	{
		$this->init();
		return $this->entity;
	}


	public function initialId(): int
	{
		return $this->id;
	}

	public function jsonSerialize(): mixed
	{
		$data = new stdClass();
		if ($this->getEntity()) {
			$data->repository = get_class($this->repository);
			$data->id = $this->id;
		}

		return $data;
	}

}
