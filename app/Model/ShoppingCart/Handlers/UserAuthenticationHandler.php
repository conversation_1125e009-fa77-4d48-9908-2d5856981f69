<?php declare(strict_types = 1);

namespace App\Model\ShoppingCart\Handlers;

use App\Model\Orm\Order\Order;
use App\Model\ShoppingCart\ShoppingCartInterface;
use App\Model\ShoppingCart\Storage\Storage;

interface UserAuthenticationHandler
{

	public function handleLoggedIn(ShoppingCartInterface $shoppingCart, Storage $storage, ?Order $order): void;

	public function handleLoggedOut(ShoppingCartInterface $shoppingCart, Storage $storage, ?Order $order): void;

}
