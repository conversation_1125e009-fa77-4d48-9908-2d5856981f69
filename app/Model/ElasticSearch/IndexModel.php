<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch;

use App\Model\Orm\EsIndex\EsIndex;
use App\Model\Orm\EsIndex\EsIndexRepository;
use App\Model\Orm\Mutation\Mutation;
use Elastica\Client;
use Elastica\Index;
use Elastica\Request;
use Throwable;

class IndexModel
{

	public function __construct(
		private readonly string $esBaseName,
		private readonly Client $client,
		private readonly EsIndexRepository $esIndexRepository,
		private readonly ConfigurationHelper $configurationHelper,
		private readonly AliasModel $aliasModel,
	)
	{
	}


	public function getIndex(EsIndex $esIndex): Index
	{
		if ($esIndex->active === 1) {
			$name = $this->aliasModel->getAliasName($esIndex);
		} else {
			$name = $esIndex->esName;
		}

		return $this->getIndexByName($name);
	}

	public function getIndexByName(string $name): Index
	{
		return $this->client->getIndex($name);
	}

	public function switchMainAliases(EsIndex $esIndex): bool
	{
		$esIndexes = $this->esIndexRepository->findBy(['type' => $esIndex->type, 'mutation' => $esIndex->mutation]);

		$data = [];
		$data['actions'] = [];
		$aliasName = null;
		foreach ($esIndexes as $esIndex) {
			if ($aliasName === null) {
				$aliasName = $this->aliasModel->getAliasName($esIndex);
			}

			if ($esIndex->active) {
				$action = 'add';
			} else {
				$action = 'remove';
			}

			$data['actions'][] = [$action => ['index' => $esIndex->esName, 'alias' => $aliasName]];
		}

		$response = $this->client->request('_aliases', Request::POST, $data);
		return $response->isOk();
	}


	public function getIndexStats(EsIndex $esIndex): array|null
	{
		try {
			return $this->getIndex($esIndex)->getStats()->getData()['_all']['total'];
		} catch (Throwable) {
			return null;
		}
	}



	public function createIndex(EsIndex $newEsIndexEntity): EsIndex
	{
		$index = $this->getIndexByName($newEsIndexEntity->esName);
		$mutation = $newEsIndexEntity->mutation;
		$type = $newEsIndexEntity->type;

		[$setting, $mapping] = $this->configurationHelper->readNeonConfig($mutation, $type);

		$setting = $this->addSynonyms($mutation, $setting);

		$data = [
			'mappings' => $mapping,
			'settings' => $setting,
		];

		$response = $index->create($data, true);
		if ($response->hasError()) {
			$newEsIndexEntity->status .= ' ' . $response->getErrorMessage();
		}

		$this->esIndexRepository->persistAndFlush($newEsIndexEntity);
		return $newEsIndexEntity;
	}



	public function findElasticSearchCreatedIndexes(): array
	{
		$response = $this->client->request('_cat/indices/' . $this->esBaseName . '*', Request::GET, [], ['format' => 'json', 'h' => 'index,store.size']);

		if ($response->isOk()) {
			return $response->getData();
		}

		return [];
	}

	public function copyDataToNewIndex(EsIndex $oldIndex, EsIndex $newEsIndex): ?EsIndex
	{
		$this->client->setConfigValue('timeout', 0);
		$response = $this->client->request('_reindex', Request::POST, [
			"source" => [
				"index" => $oldIndex->esName
			],
			"dest" => [
				"index" => $newEsIndex->esName
			]
		]);

		$this->client->setConfigValue('timeout', null);

		if ($response->isOk()) {
			return $newEsIndex;
		}

		return null;
	}

	private function addSynonyms(?Mutation $mutation, array $setting): array
	{
		$synonymArray = [];
		foreach ($mutation->synonyms as $word => $synonyms) {
			$synonymArray[] = $word . ',' . implode(Mutation::SYNONYMS_DELIMITER, (array)$synonyms);
		}

		$setting['analysis']['filter']['synonym']['type'] = 'synonym';
		$setting['analysis']['filter']['synonym']['synonyms'] = $synonymArray;

		return $setting;
	}


	public function deleteByName(string $esName): bool
	{
		$index = $this->client->getIndex($esName);

		if ($index->exists()) {
			$response = $index->delete();
			return $response->isOk();
		}

		return false;
	}

}
