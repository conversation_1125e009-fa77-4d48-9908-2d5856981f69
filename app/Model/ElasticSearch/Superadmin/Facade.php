<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Superadmin;

use App\Model\ElasticSearch\Service;
use App\Model\ElasticSearch\Superadmin\Convertor\ProductVariantData;
use App\Model\Messenger\Elasticsearch\ElasticBusWrapper;
use App\Model\Messenger\Elasticsearch\Superadmin\Message\DeleteSuperadminMessage;
use App\Model\Messenger\Elasticsearch\Superadmin\Message\ReplaceSuperadminMessage;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\EsIndex\EsIndex;
use App\Model\Orm\EsIndex\EsIndexRepository;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Product\ProductRepository;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\ProductVariant\ProductVariantRepository;
use Elastica\Exception\NotFoundException;
use Nette\Utils\ArrayHash;

class Facade
{

	public function __construct(
		private readonly Service $service,
		private readonly EsIndexRepository $esIndexRepository,
		private readonly ConvertorProvider $convertorProvider,
		private readonly ElasticBusWrapper $elasticBusWrapper,
		private readonly MutationsHolder $mutationsHolder,
		private readonly ProductVariantRepository $productVariantRepository,
	)
	{
	}

	public function deleteNow(object $object): void
	{
		$mutation = $this->mutationsHolder->getDefaultRs();
		if (($esIndex = $this->esIndexRepository->getSuperadminLastActive($mutation)) !== null) {
			try {
				$elasticEntity = new ElasticSuperadmin($object);
				$this->service->deleteDoc($esIndex, $elasticEntity);
			} catch (NotFoundException) {
				// ignore if index not found
			}
		}
	}

	public function updateNow(object $object, array $convertors = []): void
	{
		$mutation = $this->mutationsHolder->getDefaultRs();
		if (($esAllIndex = $this->esIndexRepository->getSuperadminLastActive($mutation)) !== null) {
			if ($convertors === []) {
				$convertors = $this->convertorProvider->getAll(get_class($object));
			}

			$elasticEntity = new ElasticSuperadmin($object, $convertors);

			$this->service->replaceDoc($esAllIndex, $elasticEntity);

		}
	}

	public function update(object $object): void
	{
		$mutation = $this->mutationsHolder->getDefaultRs();
		if (($esAllIndex = $this->esIndexRepository->getSuperadminLastActive($mutation)) !== null && isset($object->id)) {

			$convertors = $this->convertorProvider->getAllLikeStrings(get_class($object));
			$message = new ReplaceSuperadminMessage(get_class($object), $object->id, $esAllIndex, $convertors);
			$this->elasticBusWrapper->send($message);
		}
	}

	public function updateAfterStockProcess(Product $product): void
	{
		$mutation = $this->mutationsHolder->getDefaultRs();
		if (($esAllIndex = $this->esIndexRepository->getSuperadminLastActive($mutation)) !== null && $product->isPersisted()) {
			$convertors = [ProductVariantData::class];
			$this->elasticBusWrapper->send(new ReplaceSuperadminMessage(get_class($product), $product->id, $esAllIndex, $convertors));
		}
	}

	public function delete(object $object): void
	{
		$mutation = $this->mutationsHolder->getDefaultRs();
		if (($esAllIndex = $this->esIndexRepository->getSuperadminLastActive($mutation)) !== null && isset($object->id)) {

			$message = new DeleteSuperadminMessage(get_class($object), $object->id, $esAllIndex);
			$this->elasticBusWrapper->send($message);
		}
	}

	public function fill(EsIndex $esIndex, ?int $limit = null, bool $autoSwitch = false): void
	{
		$esIndex->errorCount = 0;
		$esIndex->errorDetail = new ArrayHash();

		$repositories = [
			ProductVariant::class => $this->productVariantRepository,
		];

		$lastClass = null;
		$lastItemId = null;
		$lastConvertorStrings = null;
		$sendFirstSignal = true;

		foreach ($repositories as $class => $repository) {
			if ($repository instanceof ProductVariantRepository) {
				$ids = $repository->findAllIdsForElasticSearch($limit, $esIndex->mutation);
			}/* else {
				$ids = $repository->findAllIds($limit);
			}*/

			$convertorStrings = $this->convertorProvider->getAllLikeStrings($class);

			foreach ($ids as $itemRow) {

				$signals = [];
				if ($sendFirstSignal) {
					$signals[] = ElasticBusWrapper::SIGNAL_FIRST;
					$sendFirstSignal = false;
				}

				$itemId = $itemRow->id;
				$this->elasticBusWrapper->send(
					new ReplaceSuperadminMessage($class, $itemId, $esIndex, $convertorStrings, $signals)
				);

				$lastClass = $class;
				$lastItemId = $itemId;
				$lastConvertorStrings = $convertorStrings;
			}
		}

		if ($lastClass !== null && $lastItemId !== null && $lastConvertorStrings !== null) {
			// send last signal
			$signals = [];
			$signals[] = ElasticBusWrapper::SIGNAL_LAST;

			if ($autoSwitch) {
				$signals[] = ElasticBusWrapper::AUTO_SWITCH;
			}

			$this->elasticBusWrapper->send(
				new ReplaceSuperadminMessage($lastClass, $lastItemId, $esIndex, $lastConvertorStrings, $signals)
			);
		}
	}

}
