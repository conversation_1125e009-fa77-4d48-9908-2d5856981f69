<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Superadmin;

use App\Model\ElasticSearch\Superadmin\Convertor\ProductVariantData;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\PostType\Banner\Model\Orm\BannerLocalization;
use App\PostType\Calendar\Model\Orm\CalendarLocalization;
use App\PostType\CalendarTag\Model\Orm\CalendarTag\CalendarTagLocalization;
use App\PostType\Discount\Model\Orm\DiscountLocalization\DiscountLocalization;
use App\PostType\Gift\Model\Orm\Gift\GiftLocalization;
use App\PostType\MenuMain\Model\Orm\MenuMainLocalization\MenuMainLocalization;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\CommonTree;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Author\Model\Orm\AuthorLocalization;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalization;
use App\PostType\Promotion\Model\Orm\Promotion\PromotionLocalization;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalization;
use App\PostType\Tag\Model\Orm\TagLocalization\TagLocalization;
use LogicException;

class ConvertorProvider
{

	private array $map;

	public function __construct(
		private readonly ProductVariantData $productVariantData,
	)
	{
		$this->map = [];
		foreach (func_get_args() as $convertor) {
			$this->map[$convertor::class] = $convertor;
		}
	}


	public function get(string $class): Convertor
	{
		return $this->map[$class] ?? throw new LogicException(sprintf("Missing convertor for '%s' class", $class));
	}


	public function getAll(string $class): array
	{
		return match ($class) {

			ProductVariant::class => [
				$this->productVariantData,
			],
			default => throw new LogicException(sprintf("Missing definition for '%s' class", $class))
		};
	}

	public function getAllLikeStrings(string $class): array
	{
		return array_map(fn(object $item) => $item::class, $this->getAll($class));
	}

}
