<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Common;

use App\Model\ElasticSearch\IndexModel;
use App\Model\Orm\EsIndex\EsIndex;
use App\Model\Orm\EsIndex\EsIndexRepository;
use App\Model\Orm\Mutation\Mutation;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\Tree;
use Elastica\Query;
use Elastica\Query\AbstractQuery;
use Elastica\Query\Range;
use Elastica\Query\Term;
use Elastica\QueryBuilder;
use Elastica\ResultSet;
use Nextras\Orm\Collection\ICollection;

class Repository extends \App\Model\ElasticSearch\Repository
{

	public function __construct(
		private readonly EsIndexRepository $esIndexRepository,
		IndexModel $indexModel,
	)
	{
		parent::__construct($indexModel);
	}


	public function fulltextSearch(Mutation $mutation, AbstractQuery $subQuery, string $q, int $size = 30, int $offset = 0): ?ResultSet
	{
		$fields = [];
		$fields[] = 'name.customEdgeNgram^2';
		$fields[] = 'content.customEdgeNgram';

		$b = new QueryBuilder();

		$must[] = $b->query()->multi_match()
			->setType('best_fields') //phrase_prefix phrase, cross_fields best_fields
			->setQuery($q)
//			->setFuzziness(3)
			->setOperator('OR')
			->setFields($fields);

		$filterBuilder = $b->query()->bool()->addMust($subQuery);
		foreach ($must as $item) {
			$filterBuilder->addMust($item);
		}

		$esIndex = $this->esIndexRepository->getCommonLastActive($mutation);

        if ($esIndex === null) {
            return null;
        }

		return $this->findByQuery($esIndex, $filterBuilder, $size, $offset, minScore: 10);
	}

	protected function getBaseMust(string $class): array
	{
		$b = new QueryBuilder();

		$must = [];
		$must[] = $b->query()->term(['isCollective' => false]);
		$must[] = $b->query()->term(['type' => ItemTypeHelper::getTypeByClass($class)]);
		$must[] = $b->query()->term(['public' => true]);
		$must[] = $b->query()->range('publicFrom', ['lte' => 'now']);
		$must[] = $b->query()->range('publicTo', ['gte' => 'now']);

		return $must;
	}

	protected function getBoolQuery(array $must): AbstractQuery
	{
		$b = new QueryBuilder();
		$boolQuery = $b->query()->bool();
		foreach ($must as $item) {
			$boolQuery->addMust($item);
		}

		return $boolQuery;
	}

	public function findByPostTypeClassAlphabet(Mutation $mutation, string $class,bool $withTopHits = true, int $limitPerLetter = 12):?ResultSet
	{
		$b = new QueryBuilder();
		$must = $this->getBaseMust($class);
		$boolQuery = $this->getBoolQuery($must);

		$esIndex = $this->esIndexRepository->getCommonLastActive($mutation);

		if ($esIndex === null) {
			return null;
		}

		$query = new Query();
		$query->setSize(0);
		$query->setFrom(0);
		$query->setQuery($boolQuery);


		$firstLetterAgg = $b->aggregation()->terms('firstLetterAgg')->setField('lastNameFirstLetter')->setSize(100);

		if ($withTopHits) {
			$topHitsAgg = $b->aggregation()->top_hits('topHitsAgg')->setSize($limitPerLetter)->setSort(['score' => 'desc']);
			$firstLetterAgg->addAggregation($topHitsAgg);
		}
		$query->addAggregation($firstLetterAgg);

		return $this->baseSearch($esIndex, $query);
	}

	public function findByPostTypeClassLetter(Mutation $mutation, string $class, string $letter):?ResultSet
	{
		$b = new QueryBuilder();

		$must = $this->getBaseMust($class);
		$must[] = $b->query()->term(['lastNameFirstLetter' => $letter]);

		$boolQuery = $this->getBoolQuery($must);

		$esIndex = $this->esIndexRepository->getCommonLastActive($mutation);

		if ($esIndex === null) {
			return null;
		}

		$query = new Query();
		$query->setSize(50000);
		$query->setFrom(0);
		$query->setQuery($boolQuery);
		return $this->baseSearch($esIndex, $query);
	}

	public function findByPostTypeClassFulltext(Mutation $mutation, string $class, string $keyword):?ResultSet
	{
		$b = new QueryBuilder();

		$must = $this->getBaseMust($class);
		$must[] = $b->query()->match('name.customEdgeNgram', $keyword);

		$boolQuery = $this->getBoolQuery($must);

		$esIndex = $this->esIndexRepository->getCommonLastActive($mutation);

		if ($esIndex === null) {
			return null;
		}

		$query = new Query();
		$query->setSize(50000);
		$query->setFrom(0);
		$query->setQuery($boolQuery);
		return $this->baseSearch($esIndex, $query);
	}
}
