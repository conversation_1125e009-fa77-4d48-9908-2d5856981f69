<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\All\Convertor;

use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalization;
use App\Model\ElasticSearch\All\Convertor;
use App\PostType\CalendarTag\Model\Orm\CalendarTag\CalendarTagLocalization;

class CalendarTagData implements Convertor
{

	public function convert(object $object): array
	{
		assert($object instanceof CalendarTagLocalization);
		$ret = [
			'id' => $object->id,
			'isSystemPage' => false,
			'type' => 'calendarTag',
			'langCode' => $object->mutation->langCode,
			'name' => $object->name . ' (' . $object->getParent()->getInternalName() . ')',
			'nameTitle' => $object->nameTitle,
			'description' => $object->description,
			'annotation' => '',
		];
		$ret['kind'] = 'calendarTag';

		return $ret;
	}

}
