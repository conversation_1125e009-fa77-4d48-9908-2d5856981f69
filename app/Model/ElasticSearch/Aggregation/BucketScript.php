<?php declare(strict_types=1);

namespace App\Model\ElasticSearch\Aggregation;

use Elastica\Aggregation\AbstractAggregation;
use Elastica\Aggregation\GapPolicyInterface;
use Elastica\Aggregation\Traits\GapPolicyTrait;

class BucketScript extends AbstractAggregation implements GapPolicyInterface
{

	use GapPolicyTrait;


	public function __construct(
		string $name,
		array $bucketsPath,
		string $script,
		?array $scriptParams = []
	)
	{
		parent::__construct($name);

		$this->setParam('buckets_path', $bucketsPath);
		$scriptData = [
			'source' => $script
		];
		if ($scriptParams !== []) {
			$scriptData['params'] = $scriptParams;
		}
		$this->setParam('script', $scriptData);

	}

	public function toArray(): array
	{
		return parent::toArray();
	}

}
