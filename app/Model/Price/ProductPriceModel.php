<?php declare(strict_types=1);

namespace App\Model\Price;

use App\Infrastructure\Latte\Functions;
use App\Model\CacheFactory;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\PriceLevel\PriceLevelModel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariantPrice\ProductVariantPrice;
use App\Model\Orm\ProductVariantPrice\ProductVariantPriceRepository;
use App\Model\Orm\Rate\Rate;
use App\Model\Orm\Rate\RateModel;
use App\Model\Orm\State\State;
use App\Model\Time\CurrentDateTimeProvider;
use Brick\Math\BigDecimal;
use Brick\Money\Currency;
use Brick\Money\Money;
use Nette\Caching\Cache;
use Nextras\Dbal\Result\Row;
use Nextras\Dbal\Utils\DateTimeImmutable;

class ProductPriceModel
{

	private Cache $cache;

	private array $allPriceLevels;

	public function __construct(
		private readonly CacheFactory $cacheFactory,
		private readonly RateModel $rateModel,
		private readonly PriceLevelModel $priceLevelModel,
		private readonly ProductVariantPriceRepository $productVariantPriceRepository,
		private readonly MoneyWrapperHelper $moneyWrapperHelper,
		private readonly CurrentDateTimeProvider $currentDateTimeProvider,
	)
	{
		$this->cache = $this->cacheFactory->create('productPrices');
		$this->allPriceLevels = $this->priceLevelModel->getAllPriceLevelByType();
	}

	public function getPriceByPriceLevel(Product $product, PriceLevel $priceLevel, Mutation $mutation, State $state, Currency $currency): Money
	{
		return $this->getPriceSummaryInfoCached($product, $mutation, $priceLevel, $state, $currency)->money;
	}

	public function getVatByPriceLevel(Product $product, PriceLevel $priceLevel, Mutation $mutation, State $state, Currency $currency): BigDecimal
	{
		return $this->getPriceSummaryInfoCached($product, $mutation, $priceLevel, $state, $currency)->vat;
	}


	private function getPriceSummaryInfoCached(Product $product, Mutation $mutation, PriceLevel $priceLevel, State $state, Currency $currency): MoneyWrapper
	{
		$cacheKey = Functions::cacheKey(
			'priceInfo',
			'P-' . $product->id,
			'M-' . $mutation->id,
			'PL-' . $priceLevel->id,
			'S-' . $state->id,
			'C-' . $currency->getCurrencyCode(),
			'FORCED-' . (int) $this->currentDateTimeProvider->forcedDateTimeIsUsed(),
		);

		$moneyWrappers = $this->cache->load($cacheKey);
		if ($moneyWrappers === null) {
			$moneyWrappers = $this->getPriceSummaryInfo($product, $mutation, $priceLevel, $currency, $this->currentDateTimeProvider->getCurrentDateTime());

			$dependencies = [
				Cache::Tags => array_merge([
					ProductVariantPrice::class,
					'productPrice-' . $product->id,
					Rate::class . '/' . $currency,
				], $product->getTemplateCacheTags()),
			];
			if ($this->currentDateTimeProvider->forcedDateTimeIsUsed()) {
				$dependencies[Cache::Expire] = '5 second';
			}

			$this->cache->save($cacheKey, $moneyWrappers, $dependencies);
		}

		$moneyWrappers = array_filter($moneyWrappers, fn(MoneyWrapper $moneyWrapper) => $this->moneyWrapperHelper->isValid($moneyWrapper));
		return array_values($moneyWrappers)[0] ?? $this->moneyWrapperHelper->createEmpty($currency);
	}

	private function getPriceSummaryInfo(Product $product, Mutation $mutation, PriceLevel $priceLevel, Currency $currency, DateTimeImmutable $now): array
	{
		$selectedCurrencyCode = $currency->getCurrencyCode();
		$variant = $product->firstVariant;

		$pricesData = $this->productVariantPriceRepository->getFutureActivePriceData($variant, $mutation, $priceLevel, $now);

		if ($pricesData === []) {
			return [];
		} else {
			$convert = $selectedCurrencyCode !== $mutation->currency->getCurrencyCode();
			return array_map(function (Row $priceRow) use ($selectedCurrencyCode, $convert) {
				if ($convert) {
					$priceRow->price_currency = $selectedCurrencyCode;
					$priceRow->price_amount = round($this->rateModel->getAmount((float) $priceRow->price_amount, $selectedCurrencyCode), 2);
				}
				return $this->moneyWrapperHelper->createFromRow($priceRow, $selectedCurrencyCode);
			}, $pricesData);
		}
	}


	public function getReferencePrice(Product $product, PriceLevel $priceLevel, Mutation $mutation): ?Money
	{
		$selectedCurrency = $mutation->getSelectedCurrency()->getCurrencyCode();
		$floatPrice = $this->getLastProductVariantLogPrice($product, $priceLevel);
		return Money::of(round($this->rateModel->getAmount($floatPrice, $selectedCurrency), 2), $selectedCurrency);
	}


	private function getLastProductVariantLogPrice(Product $product, PriceLevel $priceLevel): float
	{
		$cacheKey = 'getLastProductVariantLogPrice-P-' . $product->id . '-PL-' . $priceLevel->id;
		$lastProductVariantLogPrice = $this->cache->load($cacheKey);

		if ($lastProductVariantLogPrice === null) {
			$productVariantPriceLog = $product->firstVariant->priceLogs->toCollection()->limitBy(1)->orderBy('id', 'DESC')->getBy(['priceLevel' => $priceLevel]);
			if ($productVariantPriceLog === null) {
				$lastProductVariantLogPrice = 0.0;
			} else {
				$lastProductVariantLogPrice = $productVariantPriceLog->realOrigPrice;
			}
			$this->cache->save($cacheKey, $lastProductVariantLogPrice, [Cache::Expire => '30 minutes']);
		}

		return $lastProductVariantLogPrice;
	}

	public function getPriceLevelByStock(Product $product, PriceLevel $priceLevel): PriceLevel
	{
		if ($product->firstVariant->suplyCountStockDefault < 1 && $product->firstVariant->suplyCountStockSupplier > 0) {
			return $this->allPriceLevels[PriceLevel::TYPE_SUPPLIER];
		} else {
			return $priceLevel;
		}
	}


	public function getDiscountPriceWrapper(Product $product, Money $activePrice, PriceLevel $activePriceLevel, Mutation $mutation, State $state, Currency $currency): ?MoneyWrapper
	{
		$discountPriceLevel = $activePriceLevel->discountPrice;
		if ($discountPriceLevel !== null) {
			$discountPriceWrapper = $this->getPriceSummaryInfoCached($product, $mutation, $discountPriceLevel, $state, $currency);
			if (!$discountPriceWrapper->money->isZero() && $discountPriceWrapper->money->isLessThan($activePrice->getAmount())) {
				return $discountPriceWrapper;
			}
		}
		return null;
	}

	public function getComparatorPriceWrapper(Product $product, Money $activePrice, Mutation $mutation, State $state, Currency $currency): ?MoneyWrapper
	{
		$comparatorPriceLevel = $this->allPriceLevels[PriceLevel::TYPE_COMPARATORS] ?? null;
		if ($comparatorPriceLevel !== null) {
			$comparatorPriceWrapper = $this->getPriceSummaryInfoCached($product, $mutation, $comparatorPriceLevel, $state, $currency);
			if (!$comparatorPriceWrapper->money->isZero() && $comparatorPriceWrapper->money->isLessThan($activePrice->getAmount())) {
				return $comparatorPriceWrapper;
			}
		}
		return null;
	}

	public function getClubMemberPriceWrapper(Product $product, Money $activePrice, Mutation $mutation, State $state, Currency $currency): ?MoneyWrapper
	{
		$clubMemberPriceLevel = $this->allPriceLevels[PriceLevel::TYPE_CLUB] ?? null;
		if ($clubMemberPriceLevel !== null) {
			$clubMemberPriceWrapper = $this->getPriceSummaryInfoCached($product, $mutation, $clubMemberPriceLevel, $state, $currency);
			if (!$clubMemberPriceWrapper->money->isZero() && $clubMemberPriceWrapper->money->isLessThan($activePrice->getAmount())) {
				return $clubMemberPriceWrapper;
			}
		}
		return null;
	}

}
