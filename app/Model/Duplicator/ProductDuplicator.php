<?php declare(strict_types = 1);

namespace App\Model\Duplicator;

use App\Model\Orm\Creator;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Product\ProductRepository;
use App\Model\Orm\ProductFile\ProductFile;
use App\Model\Orm\ProductImage\ProductImage;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\ProductProduct\ProductProduct;
use App\Model\Orm\ProductTree\ProductTree;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\ProductVariant\ProductVariantRepository;
use App\Model\Orm\ProductVariantLocalization\ProductVariantLocalization;
use App\Model\Orm\ProductVariantPrice\ProductVariantPrice;
use App\Model\Orm\Supply\Supply;
use App\Model\Orm\TreeProduct\TreeProduct;
use Closure;
use Nextras\Orm\Entity\IEntity;
use Nextras\Orm\Relationships\OneHasMany;

class ProductDuplicator
{

	public function __construct(
		private readonly ProductVariantRepository $productVariantRepository,
		private readonly ProductRepository $productRepository,
	)
	{
	}


	public function duplicate(Product $product): Product
	{
		$newProduct = Creator::createNewFilledEntity($product);
		assert($newProduct instanceof Product);

		$this->duplicateLocalizations($product, $newProduct);
		$this->oneHasManyDuplicator($product->images, $newProduct->images); //must be handled before variants
		$this->duplicateVariants($product, $newProduct);
		$this->oneHasManyDuplicator($product->productTrees, $newProduct->productTrees);
		$this->oneHasManyDuplicator($product->treeProducts, $newProduct->treeProducts);
		$this->oneHasManyDuplicator($product->productProducts, $newProduct->productProducts);

		$this->productRepository->persist($product);

		$this->duplicateParameters($product, $newProduct);

		$newProduct->public = 0;
		$newProduct->vats = $product->vats;

		return $newProduct;
	}


	private function duplicateVariants(Product $product, Product $newProduct): void
	{
		foreach ($product->variants as $variant) {
			$newVariant = Creator::createNewFilledEntity($variant);
			assert($newVariant instanceof ProductVariant);
			$newVariant->product = $newProduct;

			$this->productVariantRepository->persist($newVariant);
			foreach ($newProduct->images as $productImage) {

				$variantsIds = $productImage->getVariantIds();
				$variantsIds = array_map(function (int|string $oldVariantId) use ($newVariant, $variant) {
					if ($variant->id === (int) $oldVariantId) {
						return $newVariant->id;
					} else {
						return $oldVariantId;
					}
				}, $variantsIds);
				$productImage->setVariantIds($variantsIds);
			}

			$this->oneHasManyDuplicator($variant->variantLocalizations, $newVariant->variantLocalizations, [$this->getSetMutationFunction()]);

			$this->oneHasManyDuplicator($variant->supplies, $newVariant->supplies);
			$this->duplicatePrice($variant, $newVariant);
		}
	}

	private function duplicatePrice(ProductVariant $variant, ProductVariant $newVariant): void
	{
		foreach ($variant->prices as $productVariantPrice) {
			$newProductVariantPrice = Creator::createNewFilledEntity($productVariantPrice);
			assert($newProductVariantPrice instanceof ProductVariantPrice);
			$newProductVariantPrice->productId = $newVariant->product->id;
			$newProductVariantPrice->price = $productVariantPrice->price;
			$newVariant->prices->add($newProductVariantPrice);
		}
	}


	private function oneHasManyDuplicator(OneHasMany $sourceOneHasMany, OneHasMany $targetOneHasMany, array $enrichFunctions = []): void // @phpstan-ignore-line
	{
		foreach ($sourceOneHasMany as $item) {
			$newItem = Creator::createNewFilledEntity($item);
			foreach ($enrichFunctions as $enrichFunction) {
				assert($enrichFunction instanceof Closure);
				$newItem = $enrichFunction($item, $newItem);
			}

			$targetOneHasMany->add($newItem);
		}
	}


	/**
	 * @phpstan-return  Closure(IEntity, IEntity): IEntity
	 */
	private function getSetMutationFunction(): Closure
	{
		return function ($sourceItem, $targetItem) {
			$targetItem->mutation = $sourceItem->mutation;
			return $targetItem;
		};
	}

	private function duplicateParameters(Product $product, Product $newProduct): void
	{
		$this->productRepository->cloneProductParameters($product, $newProduct);
	}


	private function duplicateLocalizations(Product $product, Product $newProduct): void
	{
		foreach ($product->productLocalizations as $productLocalization) {
			$newProductLocalization = Creator::createNewFilledEntity($productLocalization);
			assert($newProductLocalization instanceof ProductLocalization);
			$newProductLocalization->product = $newProduct;
			$this->oneHasManyDuplicator($productLocalization->files, $newProductLocalization->files);
		}
	}

}
