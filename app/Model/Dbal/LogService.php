<?php declare(strict_types = 1);

namespace App\Model\Dbal;

use App\Model\Form\FormDataLogger;
use Monolog\LogRecord;
use Nette\Utils\Json;
use Nextras\Dbal\Connection;
use Nextras\Dbal\Utils\DateTimeImmutable;

class LogService
{

	private const TABLE_NAME = 'log';

	public function __construct(
		private readonly Connection $connection,
	)
	{
	}

	public function create(LogRecord $record): void
	{
		$formatted = $record->formatted ?? '';
		$this->connection->query(
			'INSERT INTO %table (`message`, `context`, `createdAt`) VALUES (%s, %s, %dt);',
			self::TABLE_NAME,
			substr($record->message, 0, 255),
			$formatted,
			new DateTimeImmutable()
		);
	}

}
