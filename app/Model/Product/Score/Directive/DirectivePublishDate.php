<?php declare(strict_types = 1);

namespace App\Model\Product\Score\Directive;

use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\Utils\DateTime;
use <PERSON><PERSON><PERSON>\Exception\InvalidYearException;

final class DirectivePublishDate extends Directive implements DirectiveInterface
{

	final public function process(): float
	{
		if (!$publishDate = $this->getProductLocalization()->product->getParameterValueByUid(Parameter::UID_PUBLISH_DATE)) {
			return 0.0;
		}

		if (!($publishDate instanceof ParameterValue)) {
			return 0.0;
		}

		try {
			if (!$publishDateTime = DateTime::createFromFormat('d.m.Y', $publishDate->value)) {
				return 0.0;
			}
		} catch (InvalidYearException $invalidYearException) {
			return 0.0;
		}

		if ($publishDateTime->getDaysFromNow() < 60) {
			return 10.0;
		}

		if ($publishDateTime->getDaysFromNow() < 180) {
			return 7;
		}

		$yearsFromNow = $publishDateTime->getYearsFromNow();

		if ($yearsFromNow > 0 && $yearsFromNow <= 5) {
			return 5.0 - floor($yearsFromNow);
		}

		return 0.0;
	}

}
