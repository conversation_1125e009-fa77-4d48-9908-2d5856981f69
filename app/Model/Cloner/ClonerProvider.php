<?php declare(strict_types = 1);

namespace App\Model\Cloner;

use App\Model\Cloner\Exception\MissingCloner;
use App\Model\Orm\ProductLocalization\ProductLocalizationRepository;
use App\PostType\Author\Model\Orm\AuthorLocalizationRepository;
use App\PostType\Blog\Model\Orm\BlogLocalizationRepository;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalizationRepository;
use App\PostType\Page\Model\Orm\TreeRepository;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalizationRepository;
use Nextras\Orm\Entity\IEntity;

class ClonerProvider
{

	public function __construct(
		private readonly TreeRepository $treeRepository,
		private readonly ProductLocalizationRepository $productLocalizationRepository,
		private readonly BlogTagLocalizationRepository $blogTagLocalizationRepository,
		private readonly AuthorLocalizationRepository $authorLocalizationRepository,
		private readonly SeoLinkLocalizationRepository $seoLinkLocalizationRepository,
		private readonly BlogLocalizationRepository $blogLocalizationRepository,
		private readonly PageCommonCloner $pageCommonCloner,
		private readonly ProductLocalizationCommonCloner $productLocalizationCommonCloner,
		private readonly BlogTagLocalizationCommonCloner $blogTagLocalizationCommonCloner,
		private readonly AuthorLocalizationCommonCloner $authorLocalizationCommonCloner,
		private readonly SeoLinkLocalizationCommonCloner $seoLinkLocalizationCommonCloner,
		private readonly BlogLocalizationCommonCloner $blogLocalizationCommonCloner,
	)
	{}

	public function getClonerByEntity(IEntity $entity): CommonCloner
	{
		$entityClass = get_class($entity);

		return match (true) {
			in_array($entityClass, $this->treeRepository->getEntityClassNames()) => $this->pageCommonCloner,
			in_array($entityClass, $this->productLocalizationRepository->getEntityClassNames()) => $this->productLocalizationCommonCloner,
			in_array($entityClass, $this->blogTagLocalizationRepository->getEntityClassNames()) => $this->blogTagLocalizationCommonCloner,
			in_array($entityClass, $this->authorLocalizationRepository->getEntityClassNames()) => $this->authorLocalizationCommonCloner,
			in_array($entityClass, $this->seoLinkLocalizationRepository->getEntityClassNames()) => $this->seoLinkLocalizationCommonCloner,
			in_array($entityClass, $this->blogLocalizationRepository->getEntityClassNames()) => $this->blogLocalizationCommonCloner,
			default => throw new MissingCloner(sprintf("Missing definition of Cloner for '%s' entity", $entityClass))
		};
	}

}
