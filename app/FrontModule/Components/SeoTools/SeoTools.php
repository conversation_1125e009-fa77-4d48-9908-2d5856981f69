<?php declare(strict_types=1);

namespace App\FrontModule\Components\SeoTools;

use App\Components\VisualPaginator\PagerLimits;
use App\FrontModule\Components\CanonicalUrl\CanonicalUrl;
use App\FrontModule\Components\Robots\Robots;
use App\Model\BucketFilter\Box\CheckBoxes;
use App\Model\BucketFilter\Box\Slider;
use App\Model\BucketFilter\ElasticItem\DiscreteValues;
use App\Model\BucketFilter\Sort;
use App\Model\Orm\Routable;
use App\Model\StaticPage\StaticPage;
use App\Model\TranslatorDB;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Utils\Strings;
use stdClass;

/**
 * @property-read DefaultTemplate $template
 */
final class SeoTools extends UI\Control
{

	const TYPE_H1 = 'h1';
	const TYPE_TITLE = 'title';
	private ?Sort $sort = null;
	private ?stdClass $filter = null;
	private array $filterSuffix = [];
	private ?bool $filterIndexable = null;
	private array $filterParams = [];

	private bool $firstLower = true;
	private ?PagerLimits $pagerLimits = null;

	public function __construct(
		private readonly Routable|StaticPage $object,
		private readonly Robots $robots,
		private readonly CanonicalUrl $canonicalUrl,
		private readonly array $presenterFilterParameter,
		private readonly TranslatorDB $translator,
	) {

	}

	public function render(string $content, string $type = self::TYPE_H1): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->object          = $this->object;
		$this->template->sort            = $this->sort;
		$this->template->content         = trim($content);
		$this->template->type            = $type;
		$this->template->prefix          = [];
		$this->template->suffix          = [];
		$this->template->prefixSeparator = ' ';
		$this->template->suffixSeparator = ' ';



		if ($this->sort !== null) {
			if ($this->sort->isPrefixed()) {
				$this->template->prefix[] = $this->translator->translate("sort_prefix_" . $this->sort->getOrder()) . $this->template->prefixSeparator;
				$this->template->content  = $this->firstLower ? Strings::firstLower($this->template->content) : $this->template->content; // if prefixed first letter is lower
			}
			if ($this->sort->isSuffixed()) {
				$this->template->suffix[] = $this->template->suffixSeparator . $this->translator->translate("sort_suffix_" . $this->sort->getOrder());
			}
		}

		if ($this->filter !== null) {
			foreach ($this->filterSuffix as $suffixItem) {
				$this->template->suffix[] = $suffixItem;
			}
		}


		if ($this->pagerLimits !== null && $this->pagerLimits->currentPage > 1) {
			if ($type === self::TYPE_TITLE) {
				$this->template->suffixSeparator = ' | '; // if content is title text, use | as separator
			}
			$this->template->suffix[] = $this->template->suffixSeparator . str_replace(["%pages", "%page"],
					[(string) $this->pagerLimits->maxPage, (string) $this->pagerLimits->currentPage],
					$this->translator->translate('page_of_pages'));
		}


		if ($type === self::TYPE_TITLE) {
			$this->template->content = strip_tags($this->template->content);
			$this->template->suffix  = array_map('strip_tags', $this->template->suffix);
			$this->template->prefix  = array_map('strip_tags', $this->template->prefix);
		}

		if ($this->object instanceof Routable) {
			$this->template->render(__DIR__ . '/seoTools.latte');
		} else {
			$this->template->render(__DIR__ . '/seoTools_common.latte');
		}
	}

	public function setPagerLimits(?PagerLimits $pagerLimits): void
	{
		$this->pagerLimits = $pagerLimits;

		if ($this->pagerLimits !== null && $this->pagerLimits->currentPage > 1) {
			$this->robots->setForceState('noindex, follow');
		}
	}

	public function setSort(?Sort $sort): void
	{
		$this->sort = $sort;

		if ($this->sort !== null && ! $this->sort->isIndexed()) {
			$this->robots->setForceState('noindex, follow');
		} elseif ($this->sort !== null && $this->sort->isIndexed()) {
			$this->canonicalUrl->setOrder($sort->getOrder());
		}
	}

	public function setFilter(?stdClass $filter): void
	{
		$this->filter = $filter;

		$this->filterSuffix = $this->loadFilter();

		if ($this->filterIndexable !== null) {
			if ($this->filterIndexable) {
				$this->robots->setForceState('index, follow');
				$this->canonicalUrl->setFilter($this->filterParams);
			} else {
				//$this->robots->setForceState('noindex, follow');
				$this->robots->setForceState('index, follow');
				$this->canonicalUrl->setForceShowCanonical();
				$this->canonicalUrl->setFilter([]);
			}
		}
		// DEBUG MODE
		//ob_start();
		//$this->canonicalUrl->render();
		//bdump(trim(ob_get_clean()));
	}

	private function loadFilter(): array
	{
		$appliedFilters = $appliedIndexableFilters = [];

		$suffix = [];
		if ( ! isset($this->filter->boxes)) {
			return [];
		}
		foreach ($this->filter->boxes as $box) {
			if ($box instanceof CheckBoxes) {
				$checkedItems = $box->getCheckedItems();
				$countCheckedItems = count($checkedItems);
				$iteration = 1;
				foreach ($checkedItems as $item) {
					$suffixSeparator = ' ';
					if (isset($appliedFilters[$box->name])) {
						$suffixSeparator = ' a ';
					}

					$suffixValue = '';
					if ($countCheckedItems > 1 && $iteration === $countCheckedItems) {
						$suffixValue = $item->filterSuffix;
					} elseif($countCheckedItems === 1) {
						$suffixValue = $item->filterSuffix;
					}

					$titleName = ($item->filterNameForTitle !== '') ? $item->filterNameForTitle : $item->name;
					$filterRow = trim($item->filterPrefix . ' ' . $titleName . ' ' . $item->unit . ' ' . $suffixValue);

					if ($box->namespace === DiscreteValues::NAMESPACE_FLAGS) {
						$filterRow = ctype_upper($filterRow[1]) === false ? Strings::firstLower($filterRow) : $filterRow;
					}

					$suffix[] = $suffixSeparator . $filterRow;
					//if ($box->indexable) {
						$appliedIndexableFilters[$box->name][] = $item;
					//}
					$appliedFilters[$box->name][] = $item;
					$iteration++;
				}
			} elseif ($box instanceof Slider) {
				$suffixSeparator = ' ';
				$range           = null;

				if ($box->inputValueMin != $box->selectedMin && $box->inputValueMax != $box->selectedMax) {
					$range = trim($box->format($box->selectedMin) . ' ' . $box->unit . ' &ndash; ' . $box->format($box->selectedMax) . ' ' . $box->unit);
				} elseif ($box->inputValueMin != $box->selectedMin) {
					$range = $this->translator->translate('from') . ' ' . $box->format($box->selectedMin) . ' ' . $box->unit;
				} elseif ($box->inputValueMax != $box->selectedMax) {
					$range = $this->translator->translate('to') . ' ' . $box->format($box->selectedMax) . ' ' . $box->unit;
				}

				if ($range !== null) {
					$suffix[] = $suffixSeparator . trim($box->filterPrefix . ' ' . $range . ' ' . $box->filterSuffix);
					if ($box->indexable) {
						$appliedIndexableFilters[$box->name][] = $range;
					}
					$appliedFilters[$box->name][] = $range;
				}

			}
		}

		if ( ! empty($appliedIndexableFilters)) {
			$i       = 0;
			$breaked = false;
			foreach ($appliedIndexableFilters as $boxName => $items) {
				if (count($items) > 1) {
					$breaked               = true;
					$this->filterIndexable = false;
					break;
				}
				$i++;
			}

			if ($i <= 3 && ! $breaked) {
				$this->filterIndexable = true;
			}
		}

		$appliedIndexableFiltersKeys = array_keys($appliedIndexableFilters);
		foreach ($this->presenterFilterParameter as $namespace => $namespaceItems) {
			foreach ($namespaceItems as $uid => $values) {
				if (in_array($uid, $appliedIndexableFiltersKeys)) {
					$this->filterParams[$namespace][$uid] = $values;
				}
			}
		}

		if (
			(isset($this->filterParams['flags']) && isset($this->filterParams['dials']) && (count($this->filterParams['flags']) > 1 && count($this->filterParams['dials']) > 0)) ||
			(isset($this->filterParams['flags']) && count($this->filterParams['flags']) > 2) ||
			count($appliedIndexableFiltersKeys) > 2
		) {
			$this->filterIndexable = false;
		}

		return $suffix;
	}
}
