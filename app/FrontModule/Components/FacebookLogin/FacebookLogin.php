<?php declare(strict_types = 1);

namespace App\FrontModule\Components\FacebookLogin;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\User\User;
use App\Model\Orm\User\UserModel;
use App\Model\Orm\User\UserRepository;
use App\Model\Security\Acl;
use App\Model\SocialLoginService;
use App\Model\TranslatorDB;
use Nette\Application\Attributes\CrossOrigin;
use Nette\Application\UI\Control;
use Nette\Security\SimpleIdentity;
use Throwable;

/**
 * @method void onLogin()
 * @method void onEmailTaken()
 * @method void onError()
 */
final class FacebookLogin extends Control
{

	public function __construct(
		private readonly bool $isEnabled,
		private readonly Mutation $mutation,
		private readonly SocialLoginService $socialLoginService,
		private readonly TranslatorDB $translator,
		private readonly UserRepository $userRepository,
		private readonly UserModel $userModel,
	)
	{
	}

	public function render(): void
	{
		if (!$this->isEnabled) {
			return;
		}

		$this->getTemplate()->setTranslator($this->translator);
		$this->getTemplate()->setFile(__DIR__ . '/templates/facebookLogin.latte');
		$this->getTemplate()->render();
	}

	public function handleLogin(): void
	{
		$this->presenter->redirectUrl($this->socialLoginService->getFacebook()->getLoginUrl());
	}

	#[CrossOrigin]
	public function handleResponse(): void
	{
		try {
			$userData = $this->getUserData();
		} catch (Throwable $exception) {
			bdump($exception);
			$this->getPresenter()->flashMessage($this->translator->translate('facebook_login_get_data_failed', 'error'));
			$this->redirectToUserSection();
		}

		bdump($userData);
		$this->logInById($userData);
		$this->pairLoggedByEmail($userData);
		$this->register($userData);

		$this->getPresenter()->flashMessage($this->translator->translate('facebook_login_failed', 'error'));
	}

	private function logInById(array $userData): void
	{
		if (!isset($userData['id'])) {
			return;
		}

		$user = $this->userRepository->getByFacebookId($userData['id']);

		if ($user === null) {
			return;
		}

		$this->getPresenter()->getUser()->login(new SimpleIdentity($user->id, $user->role));
		$this->redirectToUserSection();
	}

	private function pairLoggedByEmail(array $userData): void
	{
		if (!isset($userData['email'], $userData['id'])) {
			return;
		}

		if (!$user = $this->userRepository->getByEmail($userData['email'], $this->mutation)) {
			return;
		}

		if ($user->facebookId === null) {
			$user->facebookId = $userData['id'];
			$this->userRepository->persistAndFlush($user);
		}

		$this->getPresenter()->getUser()->login(new SimpleIdentity($user->id, $user->role));

		$this->redirectToUserSection();
	}

	private function register(array $userData): void
	{
		if (!isset($userData['email'], $userData['id'])) {
			return;
		}

		if ($this->userRepository->getByEmail($userData['email'], $this->mutation)) {
			return;
		}

		$user = new User();
		$this->userRepository->attach($user);
		$user->role = Acl::ROLE_USER;
		$user->priceLevel = PriceLevel::DEFAULT_ID;
		$user->mutations->add($this->mutation);

		$user = $this->userModel->save($user, [
			'email' => $userData['email'],
			'facebookId' => $userData['id'],
		]);

		$this->presenter->getUser()->login(new SimpleIdentity($user->id, $user->role));
		$this->presenter->getHttpResponse()->setCookie('registrationComplete', '1', '+1 minute');
		$this->redirectToUserSection();
	}

	private function redirectToUserSection(): never
	{
		$this->getPresenter()->redirect($this->mutation->pages->userSection);
	}

	private function getUserData(): array
	{
		return $this->socialLoginService->getFacebook()->getMe([
			\VencaX\FacebookLogin::ID,
			\VencaX\FacebookLogin::EMAIL,
			\VencaX\FacebookLogin::NAME,
			\VencaX\FacebookLogin::FIRST_NAME,
			\VencaX\FacebookLogin::LAST_NAME,
		]);
	}

}
