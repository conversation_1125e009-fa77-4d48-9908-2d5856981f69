<?php declare(strict_types = 1);

namespace App\FrontModule\Components\OpenGraph\Mappers;

use App\FrontModule\Components\OpenGraph\Enums\OpenGraphMappingType;

readonly final class MapperTemplate
{

	final public function map(OpenGraphMappingType $graphMappingType): string
	{
		return match ($graphMappingType) {
			OpenGraphMappingType::category => __DIR__ . '/../templates/category.latte',
			OpenGraphMappingType::default,
			OpenGraphMappingType::product,
			OpenGraphMappingType::calendar,
			OpenGraphMappingType::article
				=> __DIR__ . '/../templates/default.latte',
			OpenGraphMappingType::sharedLibrary => __DIR__ . '/../templates/sharedLibrary.latte',
		};
	}

}
