<?php declare(strict_types = 1);

namespace App\FrontModule\Components\OpenGraph;

use App\FrontModule\Components\OpenGraph\DTO\OpenGraphDtoInterface;
use App\FrontModule\Components\OpenGraph\Enums\OpenGraphMappingType;
use App\FrontModule\Components\OpenGraph\Mappers\MapperDto;
use App\FrontModule\Components\OpenGraph\Mappers\MapperTemplate;

readonly final class Resolver
{

	public function __construct(
		private MapperDto $mapperDto,
		private MapperTemplate $mapperTemplate,
	)
	{
	}

	final public function getDto(OpenGraphMappingType $openGraphMappingType): OpenGraphDtoInterface
	{
		return $this->mapperDto->map($openGraphMappingType);
	}

	final public function getTemplate(OpenGraphMappingType $openGraphMappingType): string
	{
		return $this->mapperTemplate->map($openGraphMappingType);
	}

}
