<?php declare(strict_types = 1);

namespace App\FrontModule\Components\StructuredData\Facade\Product;

use App\FrontModule\Components\StructuredData\Facade\StructuredDataFacade;
use App\Model\DTO\Product\ProductDto;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\Model\Orm\ParameterValue\ParameterValueRepository;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\ProductVariant\ProductVariant;
use Closure;
use InvalidArgumentException;
use Nette\Utils\DateTime;

/**
 * @method ProductLocalization getData()
 */
abstract class StructuredDataProductDefaultFacade extends StructuredDataFacade
{

	protected ParameterValueRepository $parameterValueRepository;

	/**
	 * @phpstan-var Closure(Product $product, ProductVariant $variant): ProductDto $productDtoProviderCallback
	 */
	protected Closure $productDtoProviderCallback;

	protected function createImage(): string|null
	{
		$image = $this->getData()->product->firstImage;

		if ($image === null) {
			return null;
		}

		return $this->getData()->mutation->getRealDomain() . $image->url;
	}

	protected function createReview(): array
	{
		if (!$this->getData()->product->reviewsPublic->count()) {
			return [];
		}

		$reviews = [];
		foreach ($this->getData()->product->reviewsPublic as $review) {
			$reviews[] = [
				'@type' => 'Review',
				'reviewRating' => [
					'@type' => 'Rating',
					'ratingValue' => $review->stars,
					'bestRating' => 5,
				],
				'author' => [
					'@type' => 'Person',
					'name' => $review->name ?: '',
				],
				'reviewBody' => $review->text ?: '',
			];
		}

		return $reviews;
	}

	protected function createAggregateRating(): array
	{
		$reviewInfo = $this->getData()->product->reviewInfo;

		if (!isset($reviewInfo['count']) || !$reviewInfo['count']) {
			return [];
		}

		return [
			'@type' => 'AggregateRating',
			'ratingValue' => ($reviewInfo['stars'] / $reviewInfo['count']) ?: 0,
			'ratingCount' => $reviewInfo['count'],
		];
	}

	protected function createOffers(): array
	{
		$mutation = $this->getData()->mutation;
		return [
			'@offers' => [
				'@type' => 'Offer',
				'url' => $this->getLinkFactory()->linkTranslateToNette($this->getData(), ['mutation' => $mutation]),
				'priceCurrency' => $mutation->currency->getCurrencyCode(),
				'price' => $this->getData()->product->price($mutation, $this->getOrm()->priceLevel->getDefault(), $this->getOrm()->state->getDefault($mutation))->getAmount()->toFloat(),
				'availability' => $this->resolveAvailability(),
			],
		];
	}

	/**
	 * Mapping: https://schema.org/ItemAvailability
	 */
	private function resolveAvailability(): string
	{
		return match ($this->getData()->product->availability) {
			Product::AVAILABILITY_AT_SUPPLIER, Product::AVAILABILITY_ON_STOCK => 'InStock',
			Product::AVAILABILITY_PRE_ORDER => 'PreOrder',
			default => 'OutOfStock',
		};
	}

	protected function createLanguage(): array
	{
		$languages = $this->parameterValueRepository->findValues($this->getData()->product, Parameter::UID_LANGUAGE)->fetchAll();
		return array_map(fn (ParameterValue $language) => $language->internalValue, $languages);
	}

	protected function createName(): string|null
	{
		return $this->getData()->name ?? null;
	}

	protected function createPublishDate(): string|null
	{
		$parameterValue = $this->parameterValueRepository->findValues($this->getData()->product, Parameter::UID_PUBLISH_DATE)->fetch();
		if ($parameterValue === null) {
			return null;
		}

		try {
			$dateTimePublished = DateTime::createFromFormat('d.m.Y', $parameterValue->internalValue);
			if ($dateTimePublished === false) {
				return null;
			}

			return $dateTimePublished->format('Y-m-d');
		} catch (\Throwable $e) {
			// do nothing bad format of date
		}

		return null;
	}

	protected function createNumberOfPages(): string|null
	{
		$pageCountParameter = $this->parameterValueRepository->findValues($this->getData()->product, Parameter::UID_PAGE_COUNT)->fetch();
		if ($pageCountParameter === null) {
			return null;
		}

		return $pageCountParameter->internalValue;
	}

	protected function createIsbn(): string|null
	{
		if (!$variant = $this->getData()->getFirstActiveVariantByMutation($this->getData()->mutation)) {
			return null;
		}

		return $variant->isbn ?? '';
	}


	final protected function validateData(mixed $data): ProductLocalization
	{
		if ($data instanceof ProductLocalization) {
			return $data;
		}

		throw new InvalidArgumentException();
	}

	/**
	 * @phpstan-param Closure(Product $product, ProductVariant $variant): ProductDto $productDtoProviderCallback
	 */
	final public function injectDependency(
		ParameterValueRepository $parameterValueRepository,
		Closure $productDtoProviderCallback,
	): void
	{
		$this->parameterValueRepository = $parameterValueRepository;
		$this->productDtoProviderCallback = $productDtoProviderCallback;
	}

}
