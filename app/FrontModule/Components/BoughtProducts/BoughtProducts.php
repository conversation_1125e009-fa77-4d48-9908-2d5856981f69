<?php declare(strict_types = 1);

namespace App\FrontModule\Components\BoughtProducts;

use App\FrontModule\Components\CatalogProducts\CatalogProducts;
use App\FrontModule\Components\CatalogProducts\CatalogProductsData;
use App\FrontModule\Components\CatalogProducts\CatalogProductsFactory;
use App\Model\Orm\Orm;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Routable;
use App\Model\Orm\Traits\HasStaticCache;
use App\Model\Orm\User\User as UserEntity;
use App\Model\Orm\User\UserModel;
use App\Model\Orm\User\UserProvider;
use App\Model\Setup;
use App\Model\TranslatorDB;
use Nette\Application\UI\Control;
use Nextras\Orm\Collection\ICollection;

final class BoughtProducts extends Control
{

	use HasStaticCache;

	private array $boughtProductIds = [];

	private UserEntity|null $userEntity = null;

	public function __construct(
		private readonly Routable $routable,
		private readonly Setup $setup,
		private readonly int $page,
		private readonly UserModel $userModel,
		private readonly Orm $orm,
		private readonly TranslatorDB $translator,
		private readonly CatalogProductsFactory $catalogProductsFactory,
		private readonly UserProvider $userProvider,
	)
	{
	}

	final public function render(): void
	{
		if (!$this->getUserEntity()) {
			return;
		}

		$this->getTemplate()->userBoughtProducts = $this->getUserBoughtProducts();
		$this->getTemplate()->mutation = $this->setup->mutation;
		$this->getTemplate()->priceLevel = $this->setup->priceLevel;
		$this->getTemplate()->state = $this->setup->state;

		$this->getTemplate()->templates = FE_TEMPLATE_DIR;
		$this->getTemplate()->setTranslator($this->translator);
		$this->getTemplate()->setFile(__DIR__ . '/templates/boughtProducts.latte');
		$this->getTemplate()->render();
	}

	/**
	 * @return ICollection<Product>
	 */
	public function getUserBoughtProducts(int $limit = 50): ICollection
	{
		$generator = function () use ($limit) {
			$boughtProductIds = $this->getBoughtProductsIds();

			if (count($boughtProductIds) > $limit) {
				$boughtProductIds = array_slice($boughtProductIds, 0, $limit);
			}
			return $this->orm->product->findByIds($boughtProductIds);
		};
		return $this->tryLoadCache('getUserBoughtProducts', $generator);
	}

	private function getBoughtProductsIds(): array
	{
		if ($this->boughtProductIds === []) {
			$this->boughtProductIds = $this->userModel->findOrderedProductIds($this->userProvider->userSecurity);
		}

		return $this->boughtProductIds;
	}

	private function getUserEntity(): UserEntity|null
	{
		if ($this->userEntity === null) {
			$this->userEntity = $this->loadUserEntity();
		}

		return $this->userEntity;
	}

	private function loadUserEntity(): UserEntity|null
	{
		if (!$this->userProvider->userSecurity->isLoggedIn()) {
			return null;
		}

		if (!$this->userProvider->userSecurity->getId()) {
			return null;
		}

		return $this->userProvider->userEntity;
	}

	protected function createComponentCatalogProducts(): CatalogProducts
	{
		$paramsToTemplate = [
			'productTitle' => 'h3',
			'ajaxPage' => true,
			'showMoreBtn' => false,
			'class' => 'u-mb-xs',
			'cleanFilterParam' => [],
		];

		$findCatalogProductsDataCallback = function (int $limit, int $offset) {
			$totalCount = $this->getUserBoughtProducts()->countStored();
			$itemsObject = $this->getUserBoughtProducts()->limitBy($limit, $offset);

			return new CatalogProductsData(
				$itemsObject,
				$totalCount,
			);
		};

		$catalogProducts = $this->catalogProductsFactory->create(
			$this->routable,
			$this->setup,
			$findCatalogProductsDataCallback,
			$this->page,
			$paramsToTemplate,
			'catalog',
			'boughtProducts',
		);
		$catalogProducts['pager']->pageParameterName('boughtProductsPage');
		$catalogProducts->setItemPerPage(5);

		return $catalogProducts;
	}

}
