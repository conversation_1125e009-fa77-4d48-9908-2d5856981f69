<div class="b-submenu">
	<div class="b-submenu__inner">
		<div class="b-submenu__head">
			<p class="b-submenu__back">
				<button type="button" class="b-submenu__btn btn" data-action="header#toggleSubmenu">
					<span class="btn__text">
						<span class="btn__inner">
							{('arrow-left')|icon, 'btn__icon'}
							{_"btn_back"}
						</span>
					</span>
				</button>
			</p>
			<p class="b-submenu__title h3">
				{$name}
			</p>
		</div>
		<div class="b-submenu__wrap">
			<div class="b-submenu__main">
				{include '../../templates/part/menu/submenu.latte', categories: $categories, page: $page}
			</div>
			<div n:if="$side" class="b-submenu__side">
				{include '../../templates/part/box/submenu-side.latte'}
			</div>
		</div>
	</div>
</div>

{* {define #section}
	{default $type = false}
	{default $submenu = []}
	{default $limit = false}

	<p class="b-submenu__title h5">
		{if $type == 'category'}
			<span class="u-d-n@lg">{$m->nameAnchor}</span>
			<span class="u-d-n u-d-b@lg">{_"submenu_title_".$type}</span>
			<a href="{plink $m}" class="u-d-n@lg">{_"menu_show_all"}</a>
		{else}
			{_"submenu_title_".$type}
		{/if}
	</p>

	<ul n:class="b-submenu__list, count($submenu) > 9 && $limit > 9 ? b-submenu__list--cols">
		{foreach $submenu as $item}
			{var $i = ($item instanceof App\Model\Orm\Routable) ? $item : $item->page}
			{if $limit}{breakIf $iterator->getCounter() > $limit}{/if}
			<li class="b-submenu__item">
				<a href="{plink $i}" class="b-submenu__link" data-list-id="{$listId}" data-list-name="{$i->nameAnchor}">
					{$i->nameAnchor}
				</a>
			</li>
		{/foreach}
		<li n:if="count($submenu) > $limit" n:ifcontent class="b-submenu__item">
			{if $type == 'category'}
				<a href="{plink $m}" class="b-submenu__link b-submenu__link--secondary">{_"submenu_more_".$type}</a>
			{elseif $type == 'popular_authors'}
				<a href="{plink $pages->writers}" class="b-submenu__link b-submenu__link--secondary">{_"submenu_more_".$type}</a>
			{/if}
		</li>
	</ul>
{/define} *}
{* <div class="b-submenu__group b-submenu__group--category">
	{include #section, type: 'category', submenu: $submenu, limit: 16}
</div>
<div class="b-submenu__group">
	{include #section, type: 'popular_news', submenu: $submenu, limit: 9}
</div> *}
{* <div n:if="$m->uid == 'eshop'" class="b-submenu__group u-mb-last-0">
	<div class="b-submenu__section" n:if="$writers !== []">
		{include #section, type: 'popular_authors', submenu: $writers, limit: 9}
	</div>
	<div class="b-submenu__section" n:if="$themes !== []">
		{include #section, type: 'popular_themes', submenu: $themes, limit: 9}
	</div>
</div> *}
{* {if isset($topProduct) && $topProduct !== null}
	{control topTitle.'-'.$m->id}
{/if} *}
{* <p class="b-submenu__back b-submenu__back--bottom">
	<button type="button" class="b-submenu__link item-icon as-link" data-action="header#toggleSubmenu">
		{('angle-left-bold')|icon, 'item-icon__icon'}
		<span class="item-icon__text">
			{_"btn_back"}
		</span>
	</button>
</p> *}