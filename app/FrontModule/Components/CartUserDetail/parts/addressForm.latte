{varType App\Model\ShoppingCart\ShoppingCart $shoppingCart}

<div class="b-step2">
	{if $user->isLoggedIn() && $userAddresses}
		{* P<PERSON><PERSON><PERSON><PERSON>šený - výběr adres / vložit novou sadu *}
		<div class="b-step2__inner b-step2__inner--logged">
			<div class="f-basket__box u-mb-xs u-mb-sm@md">
				<h2 class="b-step2__title h3">
					{_'billing_info_choose_title'}
					{* {$sectionCount}. {_'billing_info_choose_title'}
					{php $sectionCount += 1} *}
				</h2>

				<div class="b-step2__inp-group">
					{* Adresy uživatele *}
					{if $userAddresses}
						{php $showMore = count($userAddresses) >= 2}
						<div class="c-address tw-mb-[1.2rem]" {if $showMore}data-controller="toggle-class"{/if}>
							<div n:tag-if="$showMore" class="c-address__limiter">
								<div n:foreach="$userAddresses as $k=>$address" n:class="b-address, $form['customAddress']->errors ? has-error">
									<label class="b-address__label inp-item inp-item--radio">
										<input type="radio" n:name="customAddress" value="{$k}" class="b-address__inp inp-item__inp"{if (!isset($address->last) && $form['customAddress']->value === $k) || (isset($address->last) && $address->last === true)} checked="checked"{/if}>
										<span class="b-address__inner inp-item__text">
											<div class="b-address__main">
												{include 'address/addressBox.latte', address: $address}
											</div>
											<button type="button" class="b-address__change as-link item-icon" data-controller="toggle-class" data-action="toggle-class#toggle" data-toggle-content=".f-open--{$iterator->getCounter()}">
												<span class="item-icon__text">
													{_"btn_change"}
												</span>
												{('pencil')|icon, 'item-icon__icon'}
											</button>
										</span>
									</label>

									{* Editace *}
									<div n:class="f-open, 'f-open--'.$iterator->getCounter()" data-controller="toggle-class">
										<div class="f-open__box">
											<div class="b-address__new">
												<div class="b-address__new-holder u-mb-last-0">
													<h2 class="b-address__title h4">
														{_'title_personal_info'}
													</h2>
													{*include 'address/personal.latte', class: 'u-mb-xs', form: $form, nameSuffix: '_' . $k*}
													{include 'address/invoice.latte', class: 'u-mb-xs', form: $form, nameSuffix: '_' . $k}
													{include 'address/company.latte', class: 'u-mb-xs', form: $form, nameSuffix: '_' . $k}
													{include 'address/delivery.latte', class: 'u-mb-xs', form: $form, nameSuffix: '_' . $k, treeObject: $object}
													<div data-controller="address-suggest">
														{include '../../inp.latte', class: 'b-step2__inp', form: $form, name: address_note . '_' . $k, required: false, validate: true, showError: true}
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
								<p n:if="$showMore" class="c-address__more">
									<button type="button" class="item-icon as-link" data-action="toggle-class#toggle">
										<span class="item-icon__text">
											{_"btn_show_all_addresses"}
										</span>
										{('angle-down')|icon, 'item-icon__icon'}
									</button>
								</p>
							</div>
						</div>
					{/if}

					{* Vložit nové údaje *}
					<div n:class="f-open, b-address, u-mb-sm, $form['customAddress']->errors ? has-error, u-no-print">
						{* Přidat *}
						<label class="b-address__label inp-item inp-item--radio">
							<input type="radio" n:name="customAddress" class="f-open__inp b-address__inp u-vhide inp-item__inp" value="XXX"{if $form['customAddress']->value === 'XXX'} checked="checked"{/if}>
							<div class="b-address__inner inp-item__text">
								<div class="b-address__name tw-mb-0">
									{_'another_address_form'|noescape}
								</div>
							</div>
						</label>

						<div class="f-open__box">
							<div class="b-address__new">
								<div class="b-address__new-holder u-mb-last-0">
									<h2 class="b-address__title h3">
										{_'new_address_title'}
									</h2>
									{include 'address/invoice.latte', form: $form}
									{include 'address/company.latte', form: $form}
									{include 'address/delivery.latte', form: $form, treeObject: $object}
								</div>
							</div>
						</div>
					</div>
				</div>

				{include '../../inp.latte', class: 'b-step2__inp-group', form: $form, name: infotext, rows: 3, cols: 50}

				{include #agrees}
			</div>
		</div>
	{else}
		{* Nepřihlášený / profil nemá adresu *}
		<div class="b-step2__inner">
			{* Zákaznické údaje *}
			<div class="b-step2__inp-group u-mb-last-0">
				<h2 class="b-step2__title h3">
					{_'personal_info_title'}
				</h2>

				{if $isEditEmailAllowed}
					{include $templates.'/part/box/order-login.latte', class: false}

					<div data-controller="inp-change" data-inp-change-handle-value="{link checkEmail!, email:'__email_to_replace__'}" data-inp-change-replace-value="__email_to_replace__">
						{include '../../inp.latte', class: 'b-step2__inp', form:$form, name: email, validate: true, showError: true, data: ['action' => 'change->inp-change#check', 'inp-change-target' => 'input']}
					</div>
					<div n:snippet="emailFound">
						<p n:ifset="$emailFound" class="message message--error">{_order_email_found}</p>
					</div>
				{else}
					<p>
						{_'form_label_email'}: {$userSession->email} <a n:if="!$user->isLoggedIn()" n:href="changeEmail!" data-naja data-naja-history="off">{_order_change_email}</a>
					</p>
				{/if}
				{*include 'address/invoice.latte', form: $form*}
				{*include 'address/personal.latte', form: $form*}
				{*include 'address/order-register.latte', form:$form,  class: 'u-mb-sm'*} {* TODO *}
			</div>

			{* Fakturační údaje *}
			<div class="b-step2__inp-group u-mb-last-0" data-controller="ares">
				<h2 class="b-step2__title h3">
					{_'billing_info_title'}
				</h2>
				{include 'address/invoice.latte', form: $form}
				{include 'address/company.latte', form:$form}
				{include 'address/delivery.latte', form:$form}
				{include '../../inp.latte', class: 'b-step2__inp', form: $form, name: infotext, rows: 3, cols: 50}
			</div>

			{include #agrees}
		</div>
	{/if}
</div>


{define #agrees}
	{include '../../inp.latte', class: $shoppingCart->hasExtendedStock(days: 5) ? 'u-d-n' : 'b-step2__inp', form: $form, name: questionnaire, type: checkbox}

	{if $form !== null && $form->getComponent('agrees', false) !== null && count($form['agrees']->getComponents())}
		<p class="message message--error" n:if="count($form['agrees']->getErrors()) > 0">
			{_"msg_check_all_agrees"}
		</p>

		{capture $uidConditionsLink}{plink $pages->conditions}{/capture}
		{capture $uidPersonalLink}{plink $pages->personalData}{/capture}

		{formContainer agrees}
			{foreach $formContainer->getComponents() as $checkbox}
				{var $checkboxName = $checkbox->getName()}
				{var $agreeLabel =  $object->cf->typeAgree->$checkboxName ?? $checkbox->getCaption()}
				{php $agreeLabel = str_replace(['<p>', '</p>'], [''], $agreeLabel)}
				{php $agreeLabel = str_replace(['#conditions', '#personal'], [$uidConditionsLink, $uidPersonalLink], $agreeLabel)}
				{if $formContainer[$checkboxName]->isRequired()}
					{capture $required}<span n:class="$formContainer[$checkboxName]->hasErrors() ? u-color-red">{_"label_required"}</span> {/capture}
					{php $agreeLabel = $required . $agreeLabel}
				{/if}
				{include '../inp.latte', class: 'b-step2__inp', labelClass: 'inp-label', validate: true, form: $formContainer, name: $checkbox, agreeLabel: $agreeLabel}
			{/foreach}
		{/formContainer}
	{/if}
{/define}
