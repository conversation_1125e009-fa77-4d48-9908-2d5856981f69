<div class="u-maw-11-12 u-mx-auto">
	<h1 class="u-vhide">{$object->name}</h1>
	{include $templates.'/part/box/order-steps.latte', class: 'tw-pt-[2.8rem] lg:tw-pt-[5.2rem] tw-mb-[0.8rem] md:tw-mb-[1.2rem]', currentStep: 3}

	<div n:ifcontent class="u-mb-last-0 u-mb-sm u-mb-md@lg">
		<p n:foreach="$flashes as $flash" class="message message--{$flash->type} u-mb-xs">
			<span class="message__emoji">{if $flash->type == 'ok'}✅{else}⚠️{/if}</span>
			<span class="message__content">
				{$flash->message}
			</span>
		</p>
	</div>

	{snippetArea step2Content}
		<div class="b-layout-basket b-layout-basket--step2">
			{var $showAddressForm = $user->isLoggedIn() || ($userStatus !== null && in_array($userStatus, [App\FrontModule\Components\CartUserDetail\UserStatus::WithoutSignIn, App\FrontModule\Components\CartUserDetail\UserStatus::WithoutRegistration, App\FrontModule\Components\CartUserDetail\UserStatus::Registered]))}
			{if !$showAddressForm}
				<div class="b-layout-basket__main u-mb-last-0">
					<div class="b-step2">
						{if !$user->isLoggedIn()}
							<div class="u-maw-4-12 u-mx-auto">
								{*if $userStatus === null}{* email *}
									{*include 'parts/emailForm.latte'*}
								{if $userStatus == App\FrontModule\Components\CartUserDetail\UserStatus::Unknown} {* registrace *}
									{include 'parts/unknownForm.latte'}
								{elseif $userStatus == App\FrontModule\Components\CartUserDetail\UserStatus::Known} {* login *}
									{include 'parts/knownForm.latte'}
								{/if}
							</div>
						{/if}
					</div>
				</div>
				{include $templates.'/part/box/cart-summary.latte', class: 'b-layout-basket__side'}
			{else}
				{* Výplnění nebo výběr adresy *}
				<form n:name=addressForm class="f-basket" novalidate="novalidate" data-naja data-naja-loader="body" data-naja-force-redirect data-naja-history="off" data-naja-check-disabled-btn>
					<div class="b-layout-basket__main u-mb-last-0">
						{include 'parts/addressForm.latte', form: $form}
					</div>
					{include $templates.'/part/box/cart-summary.latte', class: 'b-layout-basket__side'}


					{* Pokračovat *}
					<p class="b-layout-basket__next">
						<button n:if="$form !== null" n:name="next" class="btn btn--xl btn--secondary btn--loader" {if !($user->isLoggedIn())}disabled{/if}>
							<span class="btn__text">
								<span class="btn__inner">
									{_"btn_order_submit"}
									{('arrow-right-thin')|icon, 'btn__icon'}
								</span>
							</span>
						</button>
					</p>

					{* Zpět na dopravu a platbu *}
					<p class="b-layout-basket__prev">
						<a href="{plink $pages->step1}" class="btn btn--bd">
							<span class="btn__text">
								{_"btn_back_delivery_payment"}
							</span>
						</a>
					</p>
				</form>
			{/if}
		</div>
	{/snippetArea}
</div>


