<?php

declare(strict_types=1);

namespace App\FrontModule\Components\NewsletterForm;

use App\Exceptions\UserException;
use App\Model\Email\CommonFactory;
use App\Model\Form\CommonFormFactory;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\NewsletterEmail\NewsletterEmailModel;
use App\Model\Orm\Orm;
use Nette\Application\UI\Form as UIForm;
use Nette\Forms\Form;
use Nette\Utils\ArrayHash;
use Throwable;

final readonly class NewsletterFormFactory
{
	public function __construct(
		private CommonFormFactory $formFactory,
		private MutationHolder $mutationHolder,
		private NewsletterEmailModel $newsletterEmailModel,
		private Orm $orm,
	) {}

	public function create(bool $standalone = false): Form
	{
		$form = $this->formFactory->create($standalone ? Form::class : UIForm::class);

		$form->addText('email', 'form_label_enter_email')
			->addRule(Form::Email)
			->setHtmlType('email')
			->setRequired();

		$form->addSubmit('send');

		$form->onSuccess[] = function (Form $form, ArrayHash $values): void {
			try {
				$this->newsletterEmailModel->subscribeEmail($values->email, $this->mutationHolder->getMutation());
				$this->orm->flush();
			} catch (UserException) {
				// no-op, already subscribed
			} catch (Throwable) {
				$form->addError('newsletterError');
			}
		};

		return $form;
	}
}
