{default $type = 'input'}
{default $nameInputSpecial = NULL}
{default $dataValidate = false}
{default $required = false}

<p class="{if $dataValidate}js-validate__item{/if} {if $form[$nameInput]->hasErrors()}has-error{/if}">
	<span class="grid grid--middle">
		<span class="grid__cell size--3-12@md size--4-12@lg size--3-12@xl">
			{label $nameInput class=>"inp-label" /}:
			<span class="inp-required" n:if="$form[$nameInput]->isRequired() || $required">*</span><br/>
		</span>

		{if $nameInputSpecial}
			<span class="grid__cell size--5-12 size--4-12@sm size--3-12@md size--3-12@lg size--2-12@xl">
				<span class="inp-fix inp-fix--select">
					{input $nameInputSpecial class => 'inp-select'}
				</span>
			</span>
			<span class="grid__cell size--7-12 size--5-12@md size--4-12@lg size--3-12@xl">
				<span class="inp-fix{if $type == 'select'} inp-fix--select{/if}">
					{if $type == 'input'}
						{input $nameInput class => 'inp-text', data-validate=>$dataValidate, required=>$required}
					{else}
						{input $nameInput class => 'inp-select', data-validate=>$dataValidate, required=>$required}
					{/if}
				</span>
			</span>
		{else}
			<span class="grid__cell size--11-12@sm size--8-12@md size--7-12@lg size--5-12@xl">
				<span class="inp-fix{if $type == 'select'} inp-fix--select{/if}">
					{if $type == 'input'}
						{input $nameInput class => 'inp-text', data-validate=>$dataValidate, required=>$required}
					{else}
						{input $nameInput class => 'inp-select', data-validate=>$dataValidate, required=>$required}
					{/if}
				</span>
			</span>
		{/if}
			<span class="grid__cell size--11-12@sm size--4-12@xl">
				<span class="item-icon js-validate__message">
					{(check-polygon)|icon}
				</span>
			</span>
	</span>
</p>
