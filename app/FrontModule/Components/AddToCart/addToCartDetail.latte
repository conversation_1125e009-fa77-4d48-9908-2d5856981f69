{varType App\Model\Orm\Product\Product $product}
{varType App\Model\Orm\ProductVariant\ProductVariant $variant}

{var $hasPrice = $productDto->productAvailabilityHasPrice}
{var $showCart = $productDto->productAvailabilityIsShowCartDetail}
{if $showCart && $hasPrice}

		<form n:name="form" class="{$class}" {*data-naja data-naja-loader="body" data-naja-modal-target data-naja-modal="snippet--precart" data-naja-modal-class="b-modal--prebasket" data-naja-history="off"*}>
			<p class="f-add__wrap u-mb-0">
				{include $templates.'/part/form/part/count.latte', class: 'f-add__count '.$inpClass , variant: $variant, input: $form['quantity'], maxAmount: $productDto->productAvailabilityGetMaxAvailableAmount, disableZero: true}
				<button n:name="send" type="submit" class="btn {$btnClass}" data-id="{$product->id}" data-list-id="productDetail" data-list-name="{$product->internalName ?? ''}">
					<span class="btn__text">
						{_"btn_add_to_basket"}
					</span>
				</button>
			</p>
		</form>

{*else}
	Produkt je vyprodaný*}
{/if}
