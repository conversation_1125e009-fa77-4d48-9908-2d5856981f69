<?php declare(strict_types = 1);

namespace App\FrontModule\Presenters\Image;

use App\Model\Image\BetterImageTypeDetector;
use App\Model\Image\ImageHelper;
use App\Model\Image\ImageObjectFactory;
use App\Model\Image\MissingImageException;
use App\Model\Image\Setup\MissingImageSetupException;
use App\Model\Mutation\MutationDetector;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Orm;
use Contributte\Application\Response\ImageResponse;
use Contributte\Application\Response\StringResponse;
use Nette\Application\BadRequestException;
use Nette\Application\Responses\FileResponse;
use Nette\Application\UI\Presenter;
use Nette\Utils\UnknownImageFileException;

final class ImagePresenter extends Presenter
{

	public function __construct(
		private readonly ImageObjectFactory $imageObjectFactory,
		private readonly MutationHolder $mutationHolder,
		private readonly MutationDetector $mutationDetector,
		private readonly Orm $orm,
		private readonly BetterImageTypeDetector $betterImageTypeDetector,
	)
	{
		parent::__construct();
	}


	public function actionResample(string $filename, string $extension, string $sizeName = ''): void
	{
		if ($sizeName === '') {
			// if sizeName is missing
			// try to access www/data/images/ directory for SVG or GIF (see www/data/.htaccess)
			// if this request hit this action -> original image is missing
			$this->sendNoImageFallback('ogImage');
		}

		try {
			try {
				$extension = strtolower($extension);
				$imageObject = $this->imageObjectFactory->get($filename, $extension, $sizeName, $this->betterImageTypeDetector->detect());
				$this->sendResponse(new ImageResponse($imageObject->path, ImageHelper::getImageTypeByExtension($imageObject->ext)));
			} catch (UnknownImageFileException) {
				// send "noImage" response
				$this->sendNoImageFallback($sizeName);
			}
		} catch (MissingImageSetupException|MissingImageException) {
			// 404
			$mutation = $this->mutationDetector->detect();
			$this->mutationHolder->setMutation($mutation);
			$this->orm->setMutation($mutation);
			throw new BadRequestException('Image not found');
		}
	}


	public function sendNoImageFallback(string $sizeName): void
	{
		$imageObject = $this->imageObjectFactory->getNoImage($sizeName);
		if ($imageObject->getContentTypeByExt() == null) {
			throw new MissingImageException();
		}
		$this->sendResponse(new StringResponse($imageObject->getContent(), '', $imageObject->getContentTypeByExt()));
	}

}
