{default $class = false}
{default $alphabet = []}
{default $letterPlink = false}
{default $selectedLetter = false}

<nav n:if="count($alphabet)" n:class="m-alphabet, $class, embla" data-controller="embla">
	<div class="embla__viewport" data-embla-target="viewport">
		<ul class="m-alphabet__list grid grid--scroll grid--y-0 embla__container">
			<li n:foreach="$alphabet as $letter" class="grid__cell size--auto">
				<a href="{plink $letterPlink, letter=>($letter|escapeUrl)}" n:class="m-alphabet__link, $letter == $selectedLetter ? is-active">{$letter}</a>
			</li>
		</ul>
	</div>
	<button class="embla__btn embla__btn--prev" disabled="disabled" type="button" data-action="embla#prev" data-embla-target="prevButton">
		{('arrow-left')|icon}
		<span class="u-vhide">{_btn_prev}</span>
	</button>
	<button class="embla__btn embla__btn--next" disabled="disabled" type="button" data-action="embla#next" data-embla-target="nextButton">
		{('arrow-right')|icon}
		<span class="u-vhide">{_btn_next}</span>
	</button>
</nav>