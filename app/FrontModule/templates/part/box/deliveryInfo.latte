{varType App\Model\DeliveryDate $deliveryDate}
{default $deliveryDate = false}
{default $spanOnRequest = false}

{if $deliveryDate}
	{if $deliveryDate->isInterval()}
		{if $deliveryDate->isIntervalSameMonth()}
			{$deliveryDate->from|date:'j.'} - {$deliveryDate->to|date:'j. n.'}
		{else}
			{$deliveryDate->from|date:'j. n.'} - {$deliveryDate->to|date:'j. n.'}
		{/if}
	{elseif $deliveryDate->isToday()}
		{_'delivery_date_today'} {$deliveryDate->from|date:'j. n.'}
	{elseif $deliveryDate->isTomorrow()}
		{_'delivery_date_tomorrow'} {$deliveryDate->from|date:'j. n.'}
	{else}
		{var $dayOfTheWeek = $deliveryDate->getDayOfTheWeek()}
		{if $dayOfTheWeek == 3 || $dayOfTheWeek == 4}{_'delivery_date_at_inf'}{else}{_'delivery_date_at'}{/if}
		{translate}day_{$dayOfTheWeek}{/translate} {$deliveryDate->from|date:'j. n.'}
	{/if}

{elseif is_null($deliveryDate)}
	<span n:tag-if="$spanOnRequest" class="availability availability--unavailable">
		{_'availability_on_request'}
	</span>
{/if}
