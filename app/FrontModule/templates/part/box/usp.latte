{default $class = 'u-mb-md u-mb-3xl@md'}
{default $cf = $pages->title->cf->usp ?? false}
{default $title = $cf->title ?? false}
{default $items = $cf->items ?? []}
{default $narrow = false}


{if !$narrow}
	{default $titleClass = 'h1'}
{/if}

<div n:class="b-usp, $class">
	<div n:tag-if="$narrow" class="b-usp__inner u-maw-8-12 u-mx-auto">
		<h2 n:if="$title" n:class="b-usp__title, $titleClass">
			{$title} <span class="b-usp__emoji">👉</span>
		</h2>
		<ol class="b-usp__list">
			<li n:foreach="$items as $item" n:if="$item->text ?? false" class="b-usp__item">
				<div class="b-usp__box">
					{$item->text|texy|noescape}
				</div>
			</li>
		</ol>
	</div>
</div>