{default $class = false}
{default $title = false}

<div n:class="b-audio, $class, is-paused" data-controller="audio">
	<p n:if="$title" class="u-fw-b tw-mb-[0.8rem]">{$title}</p>
	<div class="tw-grid tw-grid-cols-[1fr_auto_minmax(max-content,1fr)] md:tw-grid-cols-[auto_1fr_4rem] tw-gap-[0.4rem_1.2rem] md:tw-gap-[2rem] tw-items-center tw-rounded-md md:tw-rounded-xl tw-bg-primary-150 tw-px-[2rem] md:tw-px-[4rem] tw-py-[1.2rem] md:tw-py-[1.6rem] tw-mb-[1.2rem] u-fw-b tw-text-[1.1rem] md:tw-text-[1.3rem]">
		{* Controls *}
		<p class="tw-flex tw-items-center u-mb-0 tw-gap-[1.2rem] tw-col-start-2 tw-col-end-3 md:tw-col-start-1 md:tw-col-end-2">
			<button type="button" class="b-audio__btn btn" data-action="click->audio#rewindPrev">
				<span class="btn__text">
					<span class="u-vhide">{_"audio_controls_prev"}</span>
					{('rewind-prev')|icon, 'btn__icon'}
				</span>
			</button>
			<button type="button" class="b-audio__btn b-audio__btn--play btn" data-action="click->audio#togglePlay">
				<span class="btn__text">
					<span class="u-vhide">{_"audio_controls_play"}</span>
					{('play')|icon, 'btn__icon'}
					{('pause')|icon, 'btn__icon u-d-n'}
				</span>
			</button>
			<button type="button" class="b-audio__btn btn" data-action="click->audio#rewindNext">
				<span class="btn__text">
					<span class="u-vhide">{_"audio_controls_next"}</span>
					{('rewind-next')|icon, 'btn__icon'}
				</span>
			</button>
		</p>

		{* Time and progress *}
		<div class="tw-flex tw-gap-[0.8rem] tw-items-center u-c-help tw-col-start-1 tw-col-end-4 md:tw-col-start-2 md:tw-col-end-3 tw-tnum" style="--progress: 0%;">
			<span data-audio-target="currentTime">00:00</span>
			<input type="range" min="0" max="100" value="0" data-audio-target="range" data-action="input->audio#updateRange"
				class="b-audio__inp tw-flex-1 tw-appearance-none tw-bg-gray-300 tw-rounded-full tw-overflow-hidden
					[&::-webkit-slider-thumb]:tw-appearance-none
					[&::-webkit-slider-thumb]:tw-h-[0.4rem]
					[&::-webkit-slider-thumb]:tw-w-[0.4rem]
					[&::-webkit-slider-thumb]:tw-bg-primary
					[&::-moz-range-thumb]:tw-h-[0.4rem]
					[&::-moz-range-thumb]:tw-w-[0.1rem]
					[&::-moz-range-thumb]:tw-bg-primary
					[&::-moz-range-thumb]:tw-border-0
					[&::-webkit-slider-runnable-track]:tw-bg-gray-300
					[&::-webkit-slider-runnable-track]:tw-rounded-full
					[&::-moz-range-track]:tw-bg-gray-300
					[&::-moz-range-track]:tw-rounded-full
					tw-accent-primary">
			<span data-audio-target="totalTime">00:00</span>
		</div>

		{* Speed *}
		<p class="u-mb-0 tw-col-start-3 tw-row-start-1">
			<span class="u-vhide">{_"audio_speed"}</span>
			<button type="button" class="as-link u-no-decoration u-c-help u-fw-b" data-audio-target="speedButton" data-action="click->audio#changeSpeed">1.00 &times;</button>
		</p>
	</div>
	<p class="b-audio__robot item-icon u-c-help tw-text-[1.3rem] md:tw-text-[1.4rem] u-mb-0">
		{('robot')|icon, 'item-icon__icon'}
		<span class="item-icon__text">
			{_"audio_robot_info"}
		</span>
	</p>
</div>