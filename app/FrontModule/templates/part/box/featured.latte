{default $class = false}
{default $cf = $object->cf->featured ?? false}

{php $product = isset($cf->product) ? $cf->product->getEntity() ?? false : false}
{php $btnText = $cf?->btnText ?? false}

<article n:if="$product" n:class="b-featured, $class">
	{php $img = $product->firstImage ?? false}
	{php $videos = $product->cf->videos ?? []}

	{define #imgBlock}
		{if $img}
			<img class="img img--4-3" srcset="
					{$img->getSize('sm')->src} 320w,
					{$img->getSize('md')->src} 560w,
					{$img->getSize('lg')->src} 750w,
					{$img->getSize('xl')->src} 1400w,
					{$img->getSize('2xl')->src} 1920w"
				sizes="(max-width: 1000px) 100vw,
						(max-width: 1620px) 50vw,
						983px"
				src="{$img->getSize('xl')->src}"
				alt="" loading="lazy">
		{else}
			<img class="img img--4-3" src="/static/img/illust/noimg.svg" alt="" loading="lazy">
		{/if}
	{/define}

	<p class="b-featured__product">
		{if count($videos)}
			<a href="{$videos[0]->video}" data-modal>
				{include #imgBlock}
				<span class="play play--other">{('play')|icon, 'play__icon'}</span>
			</a>
		{else}
			<a href="{plink $product}">{include #imgBlock}</a>
		{/if}
	</p>
	<div class="b-featured__content">
		{('bubble-ear')|icon, 'b-featured__ear'}
		<div class="b-popular__content">
			<div class="b-popular__decorations"></div>
			<div class="b-popular__content-inner">
				<p class="b-popular__approved approved">
					<img class="img img--circle" src="/static/img/illust/popular.svg" alt="" loading="lazy" width="70" height="70">
					<img class="img img--circle" src="/static/img/illust/alex.webp" alt="Alex" loading="lazy" width="70" height="70">
				</p>
				<h2 class="b-popular__title h1">{_"popular_product_title"}</h2>
				<div class="b-popular__annot u-mb-last-0">
					{$product->annotation|noescape}
				</div>
				<p class="b-popular__info">
					<b>{_"already_from"} 35 000 Kč</b>
					<b class="u-c-green u-d-b">{_"free_delivery_course"}</b>
				</p>
				<p n:if="$btnText" class="u-mb-0">
					<a href="{plink $product}" class="btn btn--lg">
						<span class="btn__text">
							{$btnText}
						</span>
					</a>
				</p>
			</div>
		</div>
	</div>
</article>