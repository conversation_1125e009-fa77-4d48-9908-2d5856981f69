{default $class = false}
{default $cf = isset($pages->userSection) ? $pages->userSection->cf->loyaltyClub ?? false : false}

{if $cf}
	{php $isRegistered = $isUserInClub ? 'registered' : 'nonregistered'}
	{php $header = $cf->$isRegistered??->loayltyHeader ?? false}
	{php $link = $cf->$isRegistered??->loayltyLink ?? false}
	{php $linkName = $cf->$isRegistered??->loayltyLinkName ?? false}
	{php $content = $cf->$isRegistered??->loayltyDescription ?? false}
	{php $page = $link ? $link->getEntity() ?? false : false}



	<div n:if="($header && $page && $linkName) || $content" n:ifcontent n:class="b-club-info, $class, u-round, u-mb-last-0">
		<p n:if="$header && $page && $linkName" class="b-club-info__title item-icon h5 u-mb-xxs">
			{('membership')|icon, 'item-icon__icon'}
			<span class="item-icon__text">
				{$header} <a href="{plink $page}">{$linkName}</a>
			</span>
		</p>
		<p n:if="$content">
			{$content}
		</p>
	</div>
{/if}