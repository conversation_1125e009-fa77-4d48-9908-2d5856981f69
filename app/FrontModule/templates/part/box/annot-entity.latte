{default $class = false}
{default $cf = $object->cf ?? false}
{default $img = isset($cf->gallery) && isset($cf->gallery->mainImage) ? $cf->gallery->mainImage->getEntity() ?? false : false}
{default $annotation = $object->cf->tagAnnotation->text ?? null}
{default $productCount = 0}
{default $titlePrefix = 'books_from'}
{default $title = $object->name}
<header n:class="b-annot, b-annot--entity, $class, u-mb-last-0" data-controller="toggle-class">
	<div class="u-mb-last-0 u-mb-xxs u-mb-xs@md">
		<h1 class="b-annot__title u-mb-xxs">
			{control seoTools, $translator->translate($titlePrefix) . ' ' . $title}
		</h1>
		<p class="h3 u-mt-0 u-mb-xxs">
			{_"writer_book_count"} {$productCount ?? $object->booksCount ?? 0}
		</p>
	</div>
	{if $img}
		<figure class="b-annot__figure u-mb-0" data-controller="clamped">
			<span class="b-annot__img img img--circle">
				<img src="{$img->getSize('sm')->src}" alt="" fetchpriority="high">
			</span>
			<figcaption class="b-annot__desc">
				<span class="b-annot__helper" data-clamped-target="content">
					{rtrim($annotation ?? '', '<br>')|noescape}
				</span>
				<button type="button" class="b-annot__more as-link u-d-n" data-clamped-target="btn" data-action="toggle-class#toggle" aria-expanded="false">
					{_"btn_show_description"}
				</button>
			</figcaption>
		</figure>
	{elseif $annotation}
		<p class="b-annot__desc u-mb-0" data-controller="clamped">
			<span class="b-annot__helper" data-clamped-target="content">
				{rtrim($annotation ?? '', '<br>')|noescape}
			</span>
			<button type="button" class="b-annot__more as-link u-d-n" data-clamped-target="btn" data-action="toggle-class#toggle">
				{_"btn_show_description"}
			</button>
		</p>
	{/if}
</header>

