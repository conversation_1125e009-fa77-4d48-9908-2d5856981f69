{default $class = 'tw-mb-[0.8rem] md:tw-mb-[2rem]'}
{default $annot = $object->cf->settings->annotation ?? false}
{default $extraTocItems = []}
{default $isLocked = false}
{default $titleLang = 'toc_title'}

{* Table of contents *}
{var $ccTocItems = []}
{foreach $object->cc as $key=>$item}
	{if isset($item[0]->tocName)}
		{php $ccTocItems[] = $item[0]->tocName}
	{/if}
{/foreach}
{var $tocItems = array_merge($extraTocItems, $ccTocItems)}

<div n:if="$annot || count($tocItems)" n:class="$class, 'tw-rounded-xl tw-p-[2rem] tw-bg-bg md:tw-p-[4rem] u-mb-last-0'">
	<p n:if="$annot" n:ifcontent class="leading tw-text-[1.5rem] md:tw-text-[1.8rem]">
		{str_replace('<br>', '', ($annot|texy))|noescape}
	</p>

	<hr n:if="$annot && count($tocItems)" class="tw-mt-[1.6rem] tw-mb-[1.6rem] md:tw-mt-[2rem] md:tw-mb-[2rem]">

	{if count($tocItems)}
		<p class="u-fw-b tw-mb-[0.8rem]">
			{_$titleLang}
		</p>
		<ul>
			<li n:foreach="$tocItems as $item">
				<a n:tag-if="!$isLocked" href="#{$item|webalize}">{$item}</a>
			</li>
		</ul>
	{/if}
</div>