{default $crossroad = isset($object->crossroad) ? $object->crossroad : new ArrayIterator()}
{default $class = false}
{default $customTitle = "title_tags"}
{default $link = false}
{default $noStyle = false}

<ul n:if="$crossroad && count($crossroad)" n:class="flags, flags--row, $class">
	<li n:foreach="$crossroad as $tagData">
		{php $tag = $tagData->tag ?? $tagData}
		{php $color = $tag->cf->settings->color ?? false}

		{if $link}
			<a n:href="$tag" n:class="flag, flag--sm, $noStyle ? ('flag--blue-light') : ($color ? 'flag--' . $color)">
				{$tag->name}{*if isset($tagData->count)} ({$tagData->count}){/if*}
			</a>
		{else}
			<span n:class="flag, flag--sm, $noStyle ? ('flag--blue-light') : ($color ? 'flag--' . $color)">
				{$tag->name}{*if isset($tagData->count)} ({$tagData->count}){/if*}
			</span>
		{/if}
	</li>
</ul>
