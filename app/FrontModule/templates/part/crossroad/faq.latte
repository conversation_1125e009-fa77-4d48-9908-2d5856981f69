{default $class = 'u-mb-md u-mb-xl@md'}
{default $items = []}
{default $btn = false}

<div n:class="b-faq, $class, 'tw-flex tw-flex-col'">
	<ul n:if="count($items)" class="b-faq__list tw-order-1">
		{foreach $items as $item}
			{if $iterator->counter == 7}
				<li class="b-faq__item">
					{include $templates.'/part/box/bnr-free-course.latte', class: false}
				</li>
			{/if}

			<li class="b-faq__item b-faq__item--question u-mb-last-0">
				{('bubble-ear')|icon, 'b-faq__bubble-ear'}
				<p n:if="$item->question ?? false" class="b-faq__question u-fw-b">
					{$item->question}
					<span class="b-faq__icon">{('question-bubble')|icon}</span>
				</p>
				<p n:if="$item->answer ?? false">
					{$item->answer|texy|noescape}
				</p>
			</li>
		{/foreach}
	</ul>

	<p class="tw-order-2 md:tw-order-3 tw-mb-[2rem] md:tw-mb-[8rem]">
		TODO BE: paging
	</p>

	<p class="tw-order-3 md:tw-order-4 tw-mb-[2rem] md:tw-mb-0 tw-rounded-xl tw-borer-[0.1rem] tw-border-solid tw-border-tile-light tw-p-[2.4rem_2rem] md:tw-p-[5rem] tw-text-center">
		<b class="tw-mb-[1.2rem] md:tw-mb-[1.6rem] tw-block">{_"faq_ask_text_detail"}</b>
		<span class="tw-flex tw-justify-center tw-flex-wrap tw-gap-[0.8rem_1.2rem]">
			<a n:if="isset($pages->faq)" href="{plink $pages->faq}" class="btn btn--bd btn--gray">
				<span class="btn__text">
					<span class="btn__inner">
						{('angle-left')|icon, 'btn__icon'}
						{_"btn_faq_back"}
					</span>
				</span>
			</a>
			<a href="#" class="btn btn--bd btn--gray" data-modal='{"medium": "fetch"}' data-snippetid="snippet--content"> {* TODO BE: snippet modal link *}
				<span class="btn__text">
					{_"btn_ask_question"}
				</span>
			</a>
		</span>
	</p>

	{include $templates.'/part/box/help.latte', class: 'tw-p-[3.2rem_0_4rem] tw-order-4 md:tw-order-2 tw-mb-[2rem] md:tw-mb-[3.2rem]'}
</div>