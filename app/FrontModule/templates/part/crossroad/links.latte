{default $class = false}
{default $cf = false}

<div n:class="c-links, $class">
    <p class="c-links__title">
        <b n:if="$cf->title ?? false">{$cf->title}</b>
    </p>
    <ul class="c-links__list">
        <li n:foreach="($cf->links ?? []) as $link" class="c-links__item">
            {php $type = $link->link->toggle}
            {php $page = isset($link->link->systemHref) && isset($link->link->systemHref->page) ? $link->link->systemHref->page->getEntity() ?? false : false}
            {php $href = $link->link->customHref??->href ?? false}
            {php $hrefNameSystem = $link->link->systemHref??->hrefName ?? false}
            {php $hrefNameCustom = $link->link->customHref??->hrefName ?? false}

            {if $type == 'systemHref' && $page}
                <a href="{plink $page}" n:ifcontent class="c-links__link item-icon">
                    {('question-circle')|icon, 'item-icon__icon'}
                    <span class="item-icon__text">
                        {if $hrefNameSystem}
                            {$hrefNameSystem}
                        {else}
                            {$page->nameAnchor}
                        {/if}
                    </span>
                </a>
            {elseif $type == 'customHref' && $href && $hrefNameCustom}
                <a href="{$href}" class="c-links__link item-icon" target="_blank" rel="noopener noreferrer">
                    {('question-circle')|icon, 'item-icon__icon'}
                    <span class="item-icon__text">
                        {$hrefNameCustom}
                    </span>
                </a>
            {/if}
        </li>
    </ul>
</div>