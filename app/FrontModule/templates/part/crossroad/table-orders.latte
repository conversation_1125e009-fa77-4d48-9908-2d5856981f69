{default $class = 'tw-mb-[2.4rem] md:tw-mb-[3.2rem]'}
{default $title = false}
{default $emptyMsg = false}
{default $showMore = false}
{default $showPager = false}
{default $maxVisibleProducts = 10000}
{default $orders = []}
{default $showProducts = $orders->countStored() > 0}

<div n:if="$showProducts || $emptyMsg" n:class="$class">
	<h2 n:if="$title" class="h3 tw-mb-[1.2rem]">{$title}</h2>
	{if $showProducts}
		<table class="c-table tw-grid-cols-[auto_1fr_auto] md:tw-grid-cols-[max-content_max-content_max-content_1fr_auto_auto] tw-text-[1.3rem] md:tw-text-[1.5rem]">
			<thead>
				<tr class="u-c-help tw-text-[1.3rem] tw-gap-[0.8rem] md:tw-gap-[0.8rem_2rem]">
					<th>{_"order_created"}</th>
					<th>{_"order_number"}</th>
					<th>{_"order_status"}</th>
					<th>{_"order_delivery_method"}</th>
					<th>{_"order_total_price_vat"}</th>
				</tr>
			</thead>
			<tbody>
				{foreach $orders as $order}
					{varType App\Model\Orm\Order\Order $order}
					<tr class="tw-gap-[0.8rem] md:tw-gap-[0.8rem_2rem] tw-p-[1.2rem_1.6rem_2rem] md:tw-p-[1.6rem_2rem_2.4rem]">
						<td><b>{$order->placedAt|date:'j. n. Y'}</b></td>
						<td class="max-md:tw-col-start-1 max-md:tw-mt-[-0.8rem]"><b>{$order->orderNumber}</b></td>
						<td class="max-md:tw-row-start-1 max-md:tw-col-start-2 max-md:tw-text-center">
							{include $templates.'/part/core/status.latte', order: $order}
						</td>
						<td class="max-md:tw-text-center max-md:tw-mt-[-0.8rem]">{$order->delivery->getName()} <a href="#" n:if="$order->delivery->information instanceof App\Model\Orm\Order\Delivery\PhysicalDeliveryInformation && $order->delivery->information->trackingCode !== null">{$order->delivery->information->trackingCode}</a></td>
						<td class="tw-text-right max-md:tw-row-start-1 max-md:tw-col-start-3"><b>{$order->getTotalPriceWithDeliveryVat()|money}</b></td>
						<td class="max-xxl:tw-row-start-4 max-xxl:tw-col-start-1 max-xxl:tw-col-end-4 md:tw-pl-[3rem]">
							<p class="u-mb-0 tw-flex tw-gap-[0.4rem] u-maw-3-12">
								<a href="{plink $mutation->pages->userOrderHistory $order->hash}" class="btn btn--bd btn--sm btn--gray max-md:tw-flex-auto">
									<span class="btn__text">
										{_"btn_detail"}
									</span>
								</a>
								<a href="{plink User:default, do => 'repeatOrder', orderHash => $order->hash}" class="btn btn--bd btn--sm btn--gray max-md:tw-flex-auto">
									<span class="btn__text">
										{_"btn_buy_again"}
									</span>
								</a>
								<a n:if="!in_array($order->state, [App\Model\Orm\Order\OrderState::Declined, App\Model\Orm\Order\OrderState::Canceled, App\Model\Orm\Order\OrderState::Declined]) && !$order->isPaid() && $order->payment?->information instanceof App\Model\Orm\Order\Payment\CardPaymentInformation && (($redirectUrl = $order->payment?->information->redirectUrl()) ?? false)" target="_blank" href="{$redirectUrl}" class="btn btn--secondary btn--sm max-md:tw-flex-auto">
									<span class="btn__text">
										{_"btn_pay"}
									</span>
								</a>
								{* <a href="#" class="btn btn--secondary btn--sm max-md:tw-flex-auto">
									<span class="btn__text">
										{_"btn_claims"}
									</span>
								</a> *}
								{* <a href="#" class="btn btn--secondary btn--sm max-md:tw-flex-auto">
									<span class="btn__text">
										{_"btn_extend"}
									</span>
								</a> *}
								{* <a href="#" class="btn btn--bd btn--sm max-md:tw-flex-auto">
									<span class="btn__text">
										{_"btn_track"}
									</span>
								</a> *}
							</p>
						</td>

						{* Produkty *}
						{var $productCount = count($order->getBuyableItems())}
						<td n:if="$productCount" class="tw-col-start-1 tw-col-end-7 max-md:tw-col-end-4">
							<p class="tw-flex tw-gap-[0.8rem] tw-p-[0.8rem_1.2rem] tw-overflow-auto u-mb-0">
								{foreach $order->getBuyableItems($maxVisibleProducts) as $orderItem}
									{continueIf $orderItem->getProduct() === null}
									{var App\Model\Orm\Product\Product $product = $orderItem->getProduct()}
									<a href="{plink $product}" class="tw-relative tw-w-[8.8rem] tw-flex-[0_0_auto]">
										{if $product->firstImage}
											<img class="img img--4-3 tw-rounded-sm" src="{$product->firstImage->getSize('sm')->src}" alt="{$product->nameAnchor}" loading="lazy" width="88" height="66">
										{else}
											<img class="img img--4-3 tw-rounded-sm" src="/static/img/illust/noimg.svg" alt="{$product->nameAnchor}" loading="lazy" width="88" height="66">
										{/if}
										<span n:if="$orderItem->amount > 1" class="flag flag--sm tw-absolute tw-top-0 tw-right-0">x{$orderItem->amount}</span>
									</a>

								{/foreach}
								{if ($plus = ($productCount - $maxVisibleProducts)) > 0}
									<a href="{plink $mutation->pages->userOrderHistory $order->hash}" class="c-orders__more">&plus;{$plus}</a>
								{/if}
							</p>
						</td>
					</tr>
				{/foreach}
			</tbody>
		</table>

		{if $showMore && isset($pages->userOrderHistory)}
			<p class="u-ta-c u-mb-0 tw-pt-[1.2rem]">
				<a href="{plink $pages->userOrderHistory}" class="btn btn--lg">
					<span class="btn__text">
						{_"btn_show_more_orders"}
					</span>
				</a>
			</p>
		{elseif $showPager}
			<div class="tw-pt-[1.2rem]" n:if="$showPager">
				{control pager}
			</div>
		{/if}
	{else}
		<p class="message message--md u-mb-0">
			<span class="message__emoji">👉</span>
			<span class="message__content">
				{$emptyMsg|noescape}
			</span>
		</p>
	{/if}
</div>
