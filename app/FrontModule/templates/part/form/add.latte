{default $variant = false}
{default $showInput = true}
{default $inpClass = false}
{default $btnClass = false}
{default $longLangs = false}
{default $addToCartPrefix = null}
{default $listId = ''}
{default $listName = ''}

{* {php bdump($productDto->priceVat->getAmount()->toFloat())} *}
<div class="tw-flex tw-gap-[1rem] tw-ml-auto">
	{if $productDto->productAvailabilityShowWatchdog || $productDto->priceVat->getAmount()->toFloat() === 0.0}
		{* Hlídat dostupnost *}
		{include $templates.'/part/form/watchdog.latte', class: $class, btnClass: $btnClass, longLangs: $longLangs, productId: $productDto->productId}
	{elseif $productDto->productAvailabilityShowCartCatalog}
		{* Pridat do kosiku *}
		{control addToCart, btnClass: $btnClass, listId: $listId ?? '', listName: $listName ?? ''}
	{/if}

	{* TODO BE: Přidat/odebrat z porovnání *}
	{php $isInCompare = false}
	<a href="#" n:class="b-product__btn, btn, $isInCompare ? 'btn--secondary' : 'btn--bd btn--gray', btn--icon, link-mask__unmask, btn--loader" data-naja data-naja-history="off">
		<span class="btn__text">
			{('compare')|icon, 'btn__icon'}
			<span class="u-vhide">{if $isInCompare}{_"btn_add_to_compare"}{else}{_"btn_remove_from_compare"}{/if}</span>
		</span>
	</a>
</div>