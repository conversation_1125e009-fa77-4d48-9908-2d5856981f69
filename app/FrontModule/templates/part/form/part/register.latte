<div class="f-open" n:if="!$user->isLoggedIn()">
	<label class="inp-item inp-item--checkbox">
		<input n:name="registerTab" value="1" class="f-open__inp inp-item__inp"/>
		<span class="inp-item__text">
			{_'title_register_form'}
		</span>
	</label>

	<div n:class="f-open__box, u-pt-xxs, $form['registerTab']->value ? is-open">
		{include '../../../../Components/inp.latte', form=>$form, name=>password, required=>true}
		{include '../../../../Components/inp.latte', form=>$form, name=>passwordVerify, required=>true}
	</div>
</div>

