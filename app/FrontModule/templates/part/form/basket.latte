{default $class = false}

<form n:class="f-basket, $class, block-loader" n:name="form" data-naja data-naja-loader="body" data-naja-force-redirect data-naja-history="off">
	<div class="b-layout-basket__main">
		<div class="f-basket__head">
			<h2 class="f-basket__title">
				{_"basket_title"}
			</h2>
			<label class="switch"  data-controller="inp-change" data-inp-change-handle-value="{link changePriceVat!, state: 'STATE'}" data-inp-change-replace-value="STATE">
				{*<span class="switch__text">{_'basket_toggle'|split:'%toggle'|first}</span>
				<input type="checkbox" class="switch__inp" {if !$cartPricesWithVatModeProvider->isActive}value="0"{else}value="1" checked{/if} data-inp-change-target="input" data-action="change->inp-change#check">
				<span class="switch__inner">
					<span class="switch__bg switch__bg--left"></span>
					<span class="switch__bg switch__bg--right"></span>
					<span class="switch__tool"></span>
				</span>
				<span class="switch__text">{_'basket_toggle'|split:'%toggle'|last}</span>*}
			</label>
		</div>
		<div class="b-cart">
			<table class="b-cart__table">
				<tbody class="b-cart__body">
					{foreach $products as $productItem}{include productItem}{/foreach}
					{foreach $classEvents as $classItem}{include classItem}{/foreach}
					{foreach $gifts as $giftItem}{include giftItem}{/foreach}
					{foreach $promotions as $promotionItem}{include promotionItem}{/foreach}

					{* {php $vouchers = $vouchers->findBy(['voucherCode->voucher->type' => [\App\Model\Orm\Voucher\Voucher::TYPE_AMOUNT, \App\Model\Orm\Voucher\Voucher::TYPE_PERCENT]])}
					{foreach $vouchers as $voucherItem}{include voucherItem}{/foreach} *}
				</tbody>
			</table>
		</div>
	</div>
</form>

{* Odložené produkty *}
{control savedForLater}

{include $templates.'/part/box/total.latte', class: 'b-layout-basket__total'}
{* TODO: voucher add *}
{*include $templates.'/part/box/voucher.latte', class: 'b-layout-basket__voucher', gifts: $gifts*}

<div class="block-loader__loader"></div>



{define productItem}
	<tr class="b-cart__row">
		{varType App\Model\Orm\Order\Product\ProductItem $productItem}
		{var $link = $presenter->link($productItem->variant->product, ['v' => $productItem->variant->id])}
		{php $productDto = $productDtoProviderCallback($productItem->variant->product, $productItem->variant)}
		<td class="b-cart__img">
			<a href="{$link}">
				{* todo: img--contain class pouze pro produkty, které nejsou kurzy *}
				{if $productItem->variant && $productItem->variant->firstImage}
					<img class="img img--4-3 img--contain" src="{$productItem->variant->firstImage->getSize('sm')->src}" alt="" loading="lazy">
				{else}
					<img class="img img--4-3 img--contain" src="/static/img/illust/noimg.svg" alt="" loading="lazy"/>
				{/if}
			</a>
			{include $templates.'/part/core/type.latte', class: 'b-cart__type flag--type', product: $productItem->variant->product}
		</td>
		<td class="b-cart__content">
			<p class="tw-mb-0">
				<a href="{$link}" class="b-cart__link u-fw-b prefetch">
					{$productItem->getName()}
				</a>
			</p>
			<p class="b-cart__variant tw-mb-0">
				Definice varianty (velikost, barva apod.)
			</p>
			{ifset $changedItems[$productItem->id]}
				<p class="b-cart__availability">
					{var App\Model\Orm\Order\RefreshResult $changedItem = $changedItems[$productItem->id]}
					{if $changedItem->availableAmount === 0}
						<b class="u-c-red">
							{_"cart_availability_not_for_sale"} {* Neprodejný *}
						</b>
					{elseif $changedItem->amount > $changedItem->availableAmount}
						<b class="u-c-green">
							{_"cart_availability_available"} {$changedItem->availableAmount} {_"stock_piece"} {* Skladem *}
						</b>
						{include $templates.'/part/core/availability.latte', class: 'u-d-b', showTooltip: false, variant: $productItem->variant, productDto=>$productDto}
						<b class="u-c-help">
							{_"cart_availability_unavailable"} {$changedItem->amount - $changedItem->availableAmount} {_"stock_piece"} {* Nedostupný *}
						</b>
					{/if}
				</p>
			{else}
				{include $templates.'/part/core/availability.latte', class: 'b-cart__availability', variant: $productItem->variant, productDto=>$productDto}
			{/ifset}
		</td>
		<td class="b-cart__price-unit">
			{$productItem->unitPriceVat|money} / {_"stock_piece"}
		</td>
		<td class="b-cart__count">
			{include $templates . '/part/form/part/count.latte',
				autosubmit: true,
				variant: $productItem->variant,
				input: $control['form']['products'][$productItem->variant->id]['quantity'],
				maxAmount: $productItem->getMaxAvailableAmount(),
				class: false
			}
		</td>
		<td class="b-cart__price u-fw-b">
			{$productItem->totalPriceVat|money}
		</td>
		<td class="b-cart__more">
			{var $removeUrl = $control->link('deleteItem!', ['variantId' => $productItem->variant->id])}
			{var $saveForLaterUrl = $control->link('saveForLaterItem!', ['orderItemId' => $productItem->id])}
			{include menu, removeUrl: $removeUrl, saveForLaterUrl: $saveForLaterUrl}
		</td>
	</tr>
{/define}
{define classItem}
	<tr class="b-cart__row">
		{varType App\Model\Orm\Order\Class\ClassItem $classItem}
		{var $variant = $classItem->getProduct()->firstVariant}
		{var $link = $presenter->link($classItem->getProduct(), ['v' => $classItem->getProduct()->firstVariant->id])}
		{php $productDto = $productDtoProviderCallback($classItem->getProduct(),$classItem->getProduct()->firstVariant)}
		<td class="b-cart__img">
			<a href="{$link}">
				{if $classItem->getProduct()->firstVariant && $classItem->getProduct()->firstVariant->firstImage}
					<img class="img img--4-3" src="{$classItem->getProduct()->firstVariant->firstImage->getSize('sm')->src}" alt="" loading="lazy">
				{else}
					<img class="img img--4-3" src="/static/img/illust/noimg.svg" alt="" loading="lazy"/>
				{/if}
			</a>
			{include $templates.'/part/core/type.latte', class: 'b-cart__type flag--type tw-min-h-[2.2rem] tw-text-[1.1rem]', product: $classItem->getProduct()}
		</td>
		<td class="b-cart__content">
			<p class="tw-mb-0">
				<a href="{$link}" class="b-cart__link u-fw-b prefetch">
					{$classItem->getName()}
				</a>
			</p>
			<p class="b-cart__variant tw-mb-0">
				{*Definice varianty (velikost, barva apod.)*}
				{$classItem->getDesc()}

			</p>
			{ifset $changedItems[$classItem->id]}
				<p class="b-cart__availability">
					{var App\Model\Orm\Order\RefreshResult $changedItem = $changedItems[$classItem->id]}
					{if $changedItem->availableAmount === 0}
						<b class="u-c-red">
							{_"cart_availability_not_for_sale"} {* Neprodejný *}
						</b>
					{elseif $changedItem->amount > $changedItem->availableAmount}
						<b class="u-c-green">
							{_"cart_availability_available"} {$changedItem->availableAmount} {_"stock_piece"} {* Skladem *}
						</b>
					{include $templates.'/part/core/availability.latte', class: 'u-d-b', showTooltip: false, variant: $variant, productDto: $productDto}
						<b class="u-c-help">
							{_"cart_availability_unavailable"} {$changedItem->amount - $changedItem->availableAmount} {_"stock_piece"} {* Nedostupný *}
						</b>
					{/if}
				</p>
			{else}
				{if $classItem->classEvent !== null}
					Počet volných míst: {$classItem->getMaxAvailableAmount()}
				{else}
					{* TODO popis online kurzu v kosiku *}
				{/if}
				<div n:if="$classItem->hasRequalification()" style="color:red;"> uhrada UP 82%</div> {* TODO *}
				{*include $templates.'/part/core/availability.latte', class: 'b-cart__availability', variant: $variant, productDto=>$productDto*}
			{/ifset}
		</td>
		<td class="b-cart__price-unit" n:if="!$cartPricesWithVatModeProvider->isActive">
			{$classItem->getUnitPriceVat(2)|money} / {_"stock_piece"}
		</td>
		<td class="b-cart__price-unit" n:if="$cartPricesWithVatModeProvider->isActive">
			{$classItem->getUnitPrice(2)|money} / {_"stock_piece"}
		</td>
		<td class="b-cart__count">
			{include $templates . '/part/form/part/count.latte',
			autosubmit: true,
			variant: $variant,
			input: $control['form']['classEvents'][$classItem->getIdentifier()]['quantity'],
			maxAmount: $classItem->getMaxAvailableAmount(),
			class: false
			}
		</td>
		<td class="b-cart__price u-fw-b" n:if="!$cartPricesWithVatModeProvider->isActive">
			{$classItem->getTotalPriceVat(2)|money}
		</td>
		<td class="b-cart__price u-fw-b" n:if="$cartPricesWithVatModeProvider->isActive">
			{$classItem->getTotalPrice(2)|money}
		</td>
		<td class="b-cart__more">
			{var $removeUrl = $control->link('deleteClass!', ['classEventIdentifier' => $classItem->getIdentifier()])}
			{var $saveForLaterUrl = $control->link('saveForLaterClass!', ['orderItemId' => $classItem->id])}
			{include menu, removeUrl: $removeUrl, safeForLaterUrl: $saveForLaterUrl}
		</td>
	</tr>
{/define}
{define giftItem}
	<tr class="b-cart__row">
		{varType App\Model\Orm\Order\Gift\GiftItem $giftItem}
		{var $link = ($product = $giftItem->giftLocalization->gift->product) !== null ? $presenter->link($product) : null}
		<td class="b-cart__img">
			<a n:tag="$link ? 'a' : 'span'" href="{$link}" class="img img--contain">
				{if ($image = $giftItem->giftLocalization->getImage()) !== null}
					<img src="{$image->getSize('sm')->src}" alt="" loading="lazy">
				{else}
					<img src="/static/img/illust/noimg.svg" alt="" loading="lazy">
				{/if}
			</a>
		</td>
		<td class="b-cart__name">
			<a n:tag="$link ? 'a' : 'span'" href="{$link}" class="u-fw-b prefetch">
				{_cart_item_gift} {$giftItem->getName()}
			</a>
			<span n:if="$product !== null" class="b-cart__writer u-d-b">
				{foreach $product->writers as $writerLocalization}
					{$writerLocalization->nameAnchor}{sep}, {/sep}
				{/foreach}
			</span>
		</td>
		<td class="b-cart__type u-mb-last-0">
			<span class="type item-icon">
				{('box')|icon, 'item-icon__icon'}
				<span class="item-icon__text">
					{_gift}
				</span>
			</span>
		</td>
		<td class="b-cart__availability">
			{if $product !== null}
				{include $templates.'/part/core/availability.latte', class: false, showTooltip: false, variant: $product->firstVariant}
			{/if}
		</td>
		<td class="b-cart__count"></td>
		<td class="b-cart__price u-fw-b">
			{$giftItem->totalPriceVat|money}
		</td>
		<td class="b-cart__remove">
			<a n:href="deleteGiftItem! giftLocalizationId: $giftItem->giftLocalization->id" data-naja data-naja-history="off" data-naja-loader="body" class="b-cart__btn">
				{('close-sm')|icon}
				<span class="u-vhide">{_"btn_delete"}</span>
			</a>
		</td>
	</tr>
{/define}

{* {define voucherItem}
	<tr class="b-cart__item b-cart__item--voucher">
		{varType App\Model\Orm\Order\Voucher\VoucherItem $voucherItem}
		<td class="b-cart__img">
			{('voucher')|icon}
		</td>
		<td class="b-cart__name u-fw-b" colspan="4">
			{_"discount_code"} "{$voucherItem->voucherCode->code}"
		</td>
		<td class="b-cart__price u-fw-b">
			{$voucherItem->totalPriceVat|money}
		</td>
		<td class="b-cart__remove">
			<a n:href="deleteVoucherItem! voucherCodeId: $voucherItem->voucherCode->id" data-naja data-naja-history="off" data-naja-loader="body" class="b-cart__btn">
				{('close-sm')|icon}
				<span class="u-vhide">{_"btn_delete"}</span>
			</a>
		</td>
	</tr>
{/define} *}

{define promotionItem}
	<tr class="b-cart__item b-cart__item--voucher">
		{varType App\Model\Orm\Order\Promotion\PromotionItem $promotionItem}
		<td class="b-cart__img">
			{('voucher')|icon} {* TODO kelly promotion icon*}
		</td>
		<td class="b-cart__name u-fw-b" colspan="4">
			{_"promotion_cart_item_text"} "{$promotionItem->getName()}"
		</td>
		<td class="b-cart__price u-fw-b">
			{$promotionItem->totalPriceVat|money}
		</td>
		<td class="b-cart__remove">
			{*<a n:href="deleteVoucherItem! voucherCodeId: $voucherItem->voucherCode->id" data-naja data-naja-history="off" data-naja-loader="body" class="b-cart__btn">
				{('close-sm')|icon}
				<span class="u-vhide">{_"btn_delete"}</span>
			</a>*}
		</td>
	</tr>
{/define}

{define menu}
	{* TODO vyřešit pro ostatní typy (produkt, dárek, služba, ...). Některé položky nejsou relervantní (výběr varianty, odložení, ...) *}
	{embed $templates.'/part/core/tooltip.latte', class: 'b-more', placement: 'bottom-start', settings: '{"trigger": "click"}'}
		{block btn}
			<button type="button" class="b-more__btn as-link">{('dots')|icon}</button>
		{/block}
		{block content}
			<div class="b-more__content" data-controller="toggle-class">
				<button type="button" class="b-more__close as-link js-tooltip-close">
					{('cross')|icon}
					<span class="u-vhide">{_"btn_close"}</span>
				</button>
				<div class="b-more__menu">
					<p n:ifset="$changeVariantUrl">
						<button type="button" class="b-more__link as-link" data-action="toggle-class#toggle" data-toggle-class="is-open-variant">{_"btn_change_variant"}</button>
					</p>
					{* TODO: odložit na pozdejsi nakup *}
					<p n:ifset="$saveForLaterUrl">
						<a href="{$saveForLaterUrl}" data-naja data-naja-history="off" data-naja-loader="body" rel="nofollow" class="b-more__link">{_"btn_postpone"}</a>
					</p>
					<hr>
					<p n:ifset="$removeUrl">
						<button type="button" class="b-more__link as-link" data-action="toggle-class#toggle" data-toggle-class="is-open-remove">{_"btn_remove_from_basket"}</button>
					</p>
				</div>
				<div class="b-more__variant" n:ifset="$changeVariantUrl">
					<p class="u-fw-b u-ta-c">
						{_"more_variant_title"}
					</p>
					{include $templates.'/part/core/variant.latte', class: 'u-ta-c u-mb-sm'}
					<p class="tw-mb-0 u-ta-c">
						<button type="button" class="btn btn--bd" data-action="toggle-class#toggle" data-toggle-class="is-open-variant">
							<span class="btn__text">
								{_"btn_back"}
							</span>
						</button>
					</p>
				</div>
				<div class="b-more__remove" n:ifset="$removeUrl">
					<p class="u-fw-b u-ta-c">
						{_"more_remove_title"}
					</p>
					<p class="b-more__btns tw-mb-0">
						<a href="{$removeUrl}" class="btn btn--bd" data-naja data-naja-history="off" data-naja-loader="body">
							<span class="btn__text">
								{_"yes"}
							</span>
						</a>
						<button type="button" class="btn btn--bd" data-action="toggle-class#toggle" data-toggle-class="is-open-remove">
							<span class="btn__text">
								{_"no"}
							</span>
						</button>
					</p>
				</div>
			</div>
		{/block}
	{/embed}
{/define}

{* <div n:if="$availableGiftCount > 0" class="b-cart-total__gifts">
	{control cartGift}
	<p n:if="($availableGiftText !== false && $availableGiftText instanceof App\Model\TranslateData) || ($availableGiftCount > 0)" class="b-cart-total__gift item-icon u-fw-b">
		{('box')|icon, 'item-icon__icon'}
		<span class="item-icon__text" n:if="$availableGiftCount > 0">
			{capture $pluralLang}{$availableGiftCount|plural: "choose_gift_1", "choose_gift_2", "choose_gift_3" }{/capture}
			{_$pluralLang|replace:'%count',(string)$availableGiftCount}
		</span>
		<span class="item-icon__text" n:if="$availableGiftText instanceof App\Model\TranslateData">
			{translate($availableGiftText)} {if $availableGiftCount > 0}{_and_choose_gift|replace:'%link', '#gifts'|noescape}{else}{_and_choose_gift_no_available}{/if}
		</span>
	</p>
</div> *}
