{default $class = false}
{default $stars = 0}
{default $rating = false}
{default $type = 'static'}
{default $percentage = $stars * 100 / 5}

{php $icon = $type == 'static' ? 'star' : 'star-rating'}

<span n:if="$percentage >= 0" n:class="stars, $class">
	<b n:if="$rating" class="stars__rating">{$rating} %</b>
	<span class="stars__inner">
		<span class="stars__icons">
			{($icon)|icon}
			{($icon)|icon}
			{($icon)|icon}
			{($icon)|icon}
			{($icon)|icon}
			<span class="stars__icons stars__icons--active" style="width: {$percentage}%;">
				{($icon)|icon}
				{($icon)|icon}
				{($icon)|icon}
				{($icon)|icon}
				{($icon)|icon}
			</span>
		</span>
	</span>
</span>
