<?php

declare(strict_types=1);

namespace App\Console\Product;

use App\Console\BaseCommand;
use App\Model\Orm\Product\ProductModel;
use App\Model\Orm\Product\ProductRepository;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name: 'product:normalize',
	description: 'Add localizations to all products'
)]
final class NormalizeCommand extends BaseCommand
{

	public function __construct(
		private readonly ProductRepository $productRepository,
		private readonly ProductModel $productModel,
	)
	{
		parent::__construct();
	}


	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->start($input);
		$this->runCommand($output);

		return $this->end(self::SUCCESS);
	}


	private function runCommand(OutputInterface $output): void
	{
		$this->productRepository->setPublicOnly(false);
		$lastId = 0;
		do {

			$fetched = $this->productRepository->findBy(['id>' => $lastId])->orderBy('id')->limitBy(100)->fetchAll();
			foreach ($fetched as $product) {
				// do the work
				$this->productModel->normalizeProduct($product);

				$output->writeln((string) $product->id);
				$lastId = $product->id;
			}

			// release the entities from the memory
			$this->productRepository->getModel()->clear();

		} while (!empty($fetched));
	}

}
