<?php declare(strict_types = 1);

namespace App\Console\ExchangeRate;

use App\Console\BaseCommand;
use App\Model\CacheFactory;
use App\Model\Orm\Orm;
use App\Model\Orm\Rate\Rate;
use App\Model\Orm\Rate\RateMapper;
use GuzzleHttp\Client;
use Nette\Caching\Cache;
use Nette\InvalidArgumentException;
use Nette\Utils\Strings;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Throwable;
use Tracy\Debugger;

#[AsCommand(
	name: 'exchange-rate:download',
	description: 'Download exchange rate for currency',
)]
// [AsCronTask(expression: '# 0 * * *')]
final class DownloadCommand extends BaseCommand
{

	protected string $downloadUrl = 'https://www.cnb.cz/cs/financni_trhy/devizovy_trh/kurzy_devizoveho_trhu/denni_kurz.txt';

	public function __construct(
		private readonly RateMapper $rateMapper,
		protected readonly Orm $orm,
		protected readonly CacheFactory $cacheFactory,
	)
	{
		parent::__construct();
	}

	protected function configure(): void
	{
		$this->addArgument('currency', InputArgument::OPTIONAL, 'Specify currency');
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->start($input);
		$currency = Strings::upper($input->getArgument('currency') ?? 'EUR');
		$output->writeln($this->getDescription() . ': ' . $currency);

		try {
			$client  = new Client();
			$request = $client->get($this->downloadUrl);

			$response = $request->getBody()->getContents();

			foreach (explode("\n", $response) as $line) {
				$cols = explode('|', $line);

				if (in_array($currency, $cols)) {
					$downloadedRate = (float) str_replace(',', '.', end($cols));
				}
			}

			if ( ! isset($downloadedRate)) {
				throw new InvalidArgumentException('Exchange rate not found.');
			}

			$output->writeln('Rate: 1 CZK = ' . $downloadedRate . ' EUR');

			if (($rate = $this->orm->rate->getBy(['currency' => $currency, 'date' => (new DateTimeImmutable())->modify('today midnight')])) === null) {
				$rate = new Rate();
				$rate->date = new DateTimeImmutable();
				$rate->currency = $currency;
				$this->orm->rate->attach($rate);
			}

			$rate->rate = $downloadedRate;

			$this->orm->rate->persistAndFlush($rate);

			$cache = $this->cacheFactory->create('productPrices');
			$cache->clean([
				Cache::Tags => [Rate::class . '/' . $currency],
			]);

			if ($this->orm->rate->findAll()->count() > 2) {
				$this->rateMapper->deleteOlderAs((new DateTimeImmutable())->modify('-30 day'));
			}

		} catch (Throwable $e) {

			Debugger::log($e, Debugger::EXCEPTION);
			$output->writeln('Exception: ' . $e->getMessage());

			return self::FAILURE;
		}

		return $this->end(self::SUCCESS);
	}

}
