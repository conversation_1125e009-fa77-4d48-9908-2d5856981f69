<?php declare(strict_types = 1);

namespace App\Console\Discount;

use App\Console\BaseCommand;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Orm;
use App\PostType\Discount\Model\Orm\DiscountLocalization\DiscountLocalizationModel;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name: 'discount:reminder',
	description: 'Remind discounts thats going to expire within 14 days',
)]
class ValidityReminder extends BaseCommand
{

	protected static $defaultName = 'discount:reminder';

	public function __construct(
		protected readonly Orm $orm,
		private readonly MutationHolder $mutationHolder,
		private readonly DiscountLocalizationModel $discountLocalizationModel
	)
	{
		parent::__construct();
	}

	protected function configure(): void
	{
		$this->addArgument('mutationCode', InputArgument::REQUIRED)
			->addArgument('notifyDayInterval', InputArgument::OPTIONAL);
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->start($input);
		$mutation = $this->orm->mutation->getByCode($input->getArgument('mutationCode'));
		$notifyDayInterval = (int) ($input->getArgument('notifyDayInterval') ?? 0);

		$this->orm->setMutation($mutation);
		$this->mutationHolder->setMutation($mutation);

		$this->discountLocalizationModel->remindExpiration($mutation, $notifyDayInterval);

		$output->writeLn('DONE');

		return $this->end(self::SUCCESS);
	}

}
