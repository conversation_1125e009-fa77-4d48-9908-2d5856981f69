parameters:
	config:
		exponea:
			endpointUri: 'https://api.eu1.exponea.com'
			projectToken: "" # set this in environment.*.neon
			publicKey: "" # set this in environment.*.neon
			privateKey: "" # set this in environment.*.neon

services:
	# tracking api http client
	- App\Exponea\Client(publicKey: %config.exponea.publicKey%, privateKey: %config.exponea.privateKey%, projectToken: %config.exponea.projectToken%, endpointUri: %config.exponea.endpointUri% )
	- App\Exponea\Customer\Customer

	# tag manager renderers
	- App\Model\TagManager\Renderer\BloomreachRenderer(debugMode: %debugMode%)
	- App\Model\TagManager\Renderer\BloomreachUserIdentifyRenderer(debugMode: %debugMode%)

	# exponea event subscriber
	- App\Model\TagManager\Bloomreach\BloomreachEventSubscriber
