<?php declare(strict_types = 1);

namespace App;

use App\Components\VisualPaginator\VisualPaginatorFactory;
use App\Model\ConfigService;
use App\Model\Currency\CurrencyHelper;
use App\Model\DbalLog;
use App\Model\Image\ImageObjectFactory;
use App\Model\Orm\Orm;
use App\Model\Orm\User\UserProvider;
use App\Model\Security\User;
use Nette\Application\UI\Presenter;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\DI\Attributes\Inject;
use Nette\DI\Container;
use Tracy\Debugger;

/**
 * @property-read User $user
 * @property-read DefaultTemplate $template
 */
abstract class BasePresenter extends Presenter
{
	#[Inject]
	public Container $context;

	#[Inject]
	public ConfigService $configService;

	#[Inject]
	public ImageObjectFactory $imageObjectFactory;

	#[Inject]
	public Orm $orm;

	#[Inject]
	public VisualPaginatorFactory $visualPaginatorFactory;

	#[Inject]
	public DbalLog $dbaLog;

	#[Inject]
	public UserProvider $userProvider;

	protected ?Model\Orm\User\User $userEntity = null;

	protected function startup(): void
	{
		if ($this->user->id && $this->user->isLoggedIn()) {
			$this->userEntity = $this->userProvider->userEntity;
		} else {
			if ($this->user->id) {
				$this->user->logout(true);
			}
		}

		parent::startup();
		if (!empty($this->configService->get('debuggerEditor'))) {
			Debugger::$editor = $this->configService->get('debuggerEditor');
		}
	}

	protected function beforeRender(): void
	{
		$this->template->webVersion = $this->configService->get('webVersion');
		$this->template->userEntity = $this->userEntity;

		$this->template->addFunction('dump', function (mixed $object, int $maxDepth = 2): void {
			$oldMaxDepth = Debugger::$maxDepth;
			Debugger::$maxDepth = $maxDepth;
			bdump($object);
			Debugger::$maxDepth = $oldMaxDepth;
		});
	}

	protected function afterRender(): void
	{
		parent::afterRender();

		// set maxDepth for router part of debug bar
		Debugger::$maxDepth = 2;
	}

	public function getUserEntity(): ?Model\Orm\User\User
	{
		return $this->userEntity;
	}

	public function getContext(): Container
	{
		return $this->context;
	}

}
