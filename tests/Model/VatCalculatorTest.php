<?php

declare(strict_types=1);

namespace App\Tests\Model;

use App\Model\VatCalculator;
use Brick\Math\BigDecimal;
use Brick\Money\Money;
use Tester\Assert;
use Tester\TestCase;
use function sprintf;

require __DIR__ . '/../bootstrap.php';

/**
 * @testCase
 */
final class VatCalculatorTest extends TestCase
{

	public function providePriceWithVat(): iterable
	{
		$standardVatRate = BigDecimal::of('21');

		return [
			[
				Money::of('100', 'CZK'),
				$standardVatRate,
				Money::of('121', 'CZK'),
			],
			[
				Money::of('1', 'CZK'),
				$standardVatRate,
				Money::of('1.21', 'CZK'),
			],
			[
				Money::of('12100', 'CZK'),
				$standardVatRate,
				Money::of('14641', 'CZK'),
			],
		];
	}

	/**
	 * @dataProvider providePriceWithVat
	 */
	public function testPriceWithVat(
		Money $basePrice,
		BigDecimal $vatRate,
		Money $expectedResult,
	): void
	{
		$priceWithVat = VatCalculator::priceWithVat($basePrice, $vatRate);
		Assert::true(
			$priceWithVat->isEqualTo($expectedResult),
			sprintf('%s should be %s', $priceWithVat, $expectedResult),
		);
	}

	public function providePriceWithoutVat(): iterable
	{
		$standardVatRate = BigDecimal::of('21');

		return [
			[
				Money::of('121', 'CZK'),
				$standardVatRate,
				Money::of('100', 'CZK'),
			],
			[
				Money::of('1.21', 'CZK'),
				$standardVatRate,
				Money::of('1', 'CZK'),
			],
			[
				Money::of('135000', 'CZK'),
				$standardVatRate,
				Money::of('111570.25', 'CZK'),
			],
		];
	}

	/**
	 * @dataProvider providePriceWithoutVat
	 */
	public function testPriceWithoutVat(
		Money $price,
		BigDecimal $vatRate,
		Money $expectedResult,
	): void
	{
		$priceWithoutVat = VatCalculator::priceWithoutVat($price, $vatRate);
		Assert::true(
			$priceWithoutVat->isEqualTo($expectedResult),
			sprintf('%s should be %s', $priceWithoutVat, $expectedResult),
		);
	}

}

(new VatCalculatorTest())->run();
