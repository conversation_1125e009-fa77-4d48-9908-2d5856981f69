<?php

declare(strict_types=1);

namespace App\Tests\Model\CustomField;

use App\Model\CustomField\CustomFields;
use App\Model\CustomField\LazyValue;
use App\PostType\Page\Model\Orm\CommonTree;
use App\Tests\Helpers\ContainerFactory;
use Tester\Assert;
use Tester\TestCase;

$containerFactory = require __DIR__ . '/../../bootstrap-integration.php';

/**
 * @testCase
 */
final class CustomFieldsTest extends TestCase
{
	public function __construct(
		private readonly ContainerFactory $containerFactory
	) {}

	public function testResolve(): void
	{
		$container = $this->containerFactory->createContainer(__DIR__ . '/config.neon');

		$customFields = $container->getByType(CustomFields::class);
		\assert($customFields instanceof CustomFields);

		$tree = new CommonTree();
		$tree->template = 'Presenter:action';
		$resolvedFields = $customFields->resolveCustomFieldsFor($tree);
		Assert::count(4, $resolvedFields);

		Assert::true(\array_key_exists('fieldRef', $resolvedFields));
		$fieldRef = $resolvedFields['fieldRef'];
		Assert::same('{"type":"group","label":"Group","items":{"text":{"type":"text","label":"Text","order":0},"suggest":{"type":"suggest","subType":"tree","label":"Suggest with referenced URL","url":{"searchParameterName":"search","link":"\/foo","params":{"foo":42}},"order":1}},"order":0}', \json_encode($fieldRef, \JSON_THROW_ON_ERROR));

		Assert::true(\array_key_exists('fieldInline', $resolvedFields));
		$fieldInline = $resolvedFields['fieldInline'];
		Assert::same('{"type":"text","label":"Inline field","order":1}', \json_encode($fieldInline, \JSON_THROW_ON_ERROR));

		Assert::true(\array_key_exists('fieldTotallyInline', $resolvedFields));
		$fieldTotallyInline = $resolvedFields['fieldTotallyInline'];
		Assert::same('{"type":"text","label":"Totally inline field","order":2}', \json_encode($fieldTotallyInline, \JSON_THROW_ON_ERROR));

		Assert::true(\array_key_exists('fieldExtended', $resolvedFields));
		$fieldExtended = $resolvedFields['fieldExtended'];
		Assert::same('{"type":"suggest","subType":"tree","label":"Suggest with extends","url":{"searchParameterName":"search","link":"\/foo","params":{"foo":42}},"order":3}', \json_encode($fieldExtended, \JSON_THROW_ON_ERROR));
	}

	public function testProcess(): void
	{
		$container = $this->containerFactory->createContainer(__DIR__ . '/config.neon');

		$customFields = $container->getByType(CustomFields::class);
		\assert($customFields instanceof CustomFields);

		$tree = new CommonTree();
		$tree->template = 'Presenter:action';

		$data = (object) [
			'fieldRef' => [
				(object) [
					'text' => 'Nested text value',
					'suggest' => 1,
				],
			],
			'fieldInline' => 'Text value',
		];

		$show = $customFields->prepareForToShow($tree, $data);
		Assert::same('Text value', $show->fieldInline);
		Assert::same('Nested text value', $show->fieldRef->text);
		Assert::type(LazyValue::class, $show->fieldRef->suggest);
		Assert::same(1, $show->fieldRef->suggest->initialId());

		$data = (object) [
			'fieldRef' => [
				(object) [
					'text' => 'Nested text value',
					'suggest' => 1,
				],
			],
			'fieldInline' => 'Text value',
		];

		$showInRs = $customFields->prepareForToShow($tree, $data, inRs: true);
		Assert::same('Text value', $showInRs->fieldInline);
		Assert::type('array', $showInRs->fieldRef);
		Assert::count(1, $showInRs->fieldRef);
		Assert::same('Nested text value', $showInRs->fieldRef[0]->text);
		Assert::same(1, $showInRs->fieldRef[0]->suggest['id']);
	}
}

(new CustomFieldsTest($containerFactory))->run();
