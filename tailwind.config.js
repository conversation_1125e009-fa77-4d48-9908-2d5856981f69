const config = require('./tasks/helpers/getConfig.js');

module.exports = {
	content: ['./app/FrontModule/**/*.latte', './app/PostType/**/*.latte'],
	prefix: 'tw-',
	theme: {
		screens: config.mediaQueries.breakpoints,
		extend: {
			colors: {
				black: 'var(--color-black)',
				white: {
					DEFAULT: 'var(--color-white)',
					hover: 'var(--color-white-hover)',
				},
				orange: 'var(--color-orange)',
				gray: {
					DEFAULT: 'var(--color-gray)',
					500: 'var(--color-gray-500)',
					900: 'var(--color-gray-900)',
				},
				yellow: {
					DEFAULT: 'var(--color-yellow-600)',
					600: 'var(--color-yellow-600)',
					150: 'var(--color-yellow-150)',
					50: 'var(--color-yellow-50)',
				},
				red: {
					DEFAULT: 'var(--color-red)',
					200: 'var(--color-red-200)',
				},
				violet: {
					DEFAULT: 'var(--color-violet)',
					200: 'var(--color-violet-200)',
				},
				green: {
					DEFAULT: 'var(--color-green)',
					200: 'var(--color-green-200)',
				},
				primary: {
					DEFAULT: 'var(--color-primary)',
					150: 'var(--color-primary-150)',
					300: 'var(--color-primary-300)',
					700: 'var(--color-primary-700)',
					750: 'var(--color-primary-750)',
					hover: 'var(--color-primary-hover)',
				},
				blue: 'var(--color-blue)',
				superprimary: 'var(--color-superprimary)',
				secondary: 'var(--color-secondary)',
				peach: {
					DEFAULT: 'var(--color-peach)',
					800: 'var(--color-peach-800)',
					600: 'var(--color-peach-600)',
					300: 'var(--color-peach-300)',
					150: 'var(--color-peach-150)',
					100: 'var(--color-peach-100)',
				},
				alert: {
					DEFAULT: 'var(--color-alert)',
					light: 'var(--color-alert-light)',
					2: 'var(--color-alert2)',
					'2-light': 'var(--color-alert2-light)',
				},
				status: {
					valid: {
						DEFAULT: 'var(--color-status-valid)',
						light: 'var(--color-status-valid-light)',
					},
					invalid: {
						DEFAULT: 'var(--color-status-invalid)',
						light: 'var(--color-status-invalid-light)',
					},
				},
				inverse: {
					help: 'var(--color-inverse-help)',
					link: 'var(--color-inverse-link)',
				},
				help: 'var(--color-help)',
				tile: {
					DEFAULT: 'var(--color-tile)',
					light: 'var(--color-tile-light)',
				},
				surface: 'var(--color-surface)',
				placeholder: 'var(--color-placeholder)',
				icon: {
					minor: 'var(--color-icon-minor)',
				},
				text: {
					DEFAULT: 'var(--color-text)',
					headline: 'var(--color-text-headline)',
				},
				bd: 'var(--color-bd)',
				bg: 'var(--color-bg)',
				link: 'var(--color-link)',
				hover: 'var(--color-hover)',
			},
			borderRadius: {
				sm: 'var(--border-radius-sm)',
				md: 'var(--border-radius-md)',
				lg: 'var(--border-radius-lg)',
				xl: 'var(--border-radius-xl)',
			},
			fontFamily: {
				primary: 'var(--font-primary)',
				secondary: 'var(--font-secondary)',
			},
			boxShadow: {
				custom: '0px 2px 2px 0px rgba(0, 14, 71, 0.04), 0px 9px 14px 0px rgba(0, 14, 71, 0.1)',
			},
		},
	},
	plugins: [
		require('@tailwindcss/container-queries'),
		function ({ addUtilities }) {
			addUtilities({
				'.tnum': { fontFeatureSettings: "'tnum' 1" },
			});
		},
	],
};
