import examples from 'libphonenumber-js/examples.mobile.json';
import { getExampleNumber } from 'libphonenumber-js/min';

export class Placeholder {
	constructor(options, countryCode) {
		this.options = options;
		this.countryCode = countryCode;
		this.example = this.getExample();
	}

	get() {
		if (this.element) return this.element;
		return this.create();
	}

	create() {
		const { className } = this.options;
		this.element = document.createElement('span');
		this.element.classList.add(`${className}__placeholder`);
		this.setPlaceholder();

		return this.element;
	}

	setCountry(countryCode) {
		this.countryCode = countryCode;
		this.example = this.getExample();
		this.setPlaceholder();
	}

	setPlaceholder(value = '', fill = true) {
		this.element.textContent = value + (fill ? this.example.slice(value.length) : '');
	}

	setDefaultPlaceholder() {
		this.setPlaceholder();
	}

	getExample() {
		const example = getExampleNumber(this.countryCode, examples);
		if (example) {
			return example.formatNational();
		}

		return '';
	}
}
