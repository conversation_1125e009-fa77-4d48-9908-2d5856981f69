import { Controller } from '@hotwired/stimulus';
import loadGmapsApi from '../tools/loadGmapsApi';

// see https://bitbucket.org/superkoders/snippets/src/master/src/js/components/gmap.js for more customization
export default class gmap extends Controller {
	static values = {
		mapcenter: Object,
		markers: Array,
		zoom: Number,
	};
	static api = null;

	map = null;
	markers = [];

	connect() {
		const { options } = window.App;

		if (!options) {
			console.warn('Gmap: V App.run() nejsou definovane options!');
			return;
		}

		if (!this.hasMarkersValue && !this.hasMapcenterValue) {
			console.warn('Gmap: Either markers or map center has to be defined!');
			return;
		}

		if (gmap.api) {
			this.loadMap();
		} else {
			loadGmapsApi().then((api) => {
				gmap.api = api;
				this.loadMap();
			});
		}
	}

	loadMap() {
		this.element.classList.remove('is-loading');
		const { options } = window.App;
		const center = this.hasMapcenterValue ? this.mapcenterValue : this.markersValue[0].position;
		const icon = {
			url: `${options.assetsUrl}img/bg/marker.png`,
			scaledSize: new google.maps.Size(51, 65), // retina
		};
		const clusterStyles = [
			{
				url: `${options.assetsUrl}img/bg/cluster.png`,
				height: 50,
				width: 50,
				anchor: [0, 0],
				textColor: '#172449',
				textSize: 20,
			},
		];
		const mapOptions = {
			center,
			zoom: this.zoomValue || 16,
		};
		const bounds = new gmap.api.LatLngBounds();

		this.map = new gmap.api.Map(this.element, mapOptions);

		this.markersValue.forEach((marker) => {
			this.markers.push(
				new gmap.api.Marker({
					icon,
					map: this.map,
					position: marker.position,
				}),
			);
			bounds.extend(marker.position);
		});

		new window.MarkerClusterer(this.map, this.markers, { styles: clusterStyles });

		if (this.markers.length > 1) {
			this.map.fitBounds(bounds);
		}
	}
}
