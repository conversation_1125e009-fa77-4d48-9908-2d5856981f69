import { Controller } from '@hotwired/stimulus';
// import { useThrottle } from 'stimulus-use';

export default class extends Controller {
	static targets = ['gradient'];

	connect() {
		setTimeout(() => {
			this.rect = this.element.getBoundingClientRect();
			this.animationFrame = null;
			this.offsetCorrection = -30;
		}, 0);

		this.element.addEventListener('mousemove', (e) => this.moveGradient(e));
		this.element.addEventListener('mouseenter', (e) => this.setGradientImmediately(e));
		this.element.addEventListener('mouseleave', () => this.stopAnimation());
		window.addEventListener('resize', () => this.updateRect());
	}

	moveGradient(e) {
		const offsetX = e.clientX - this.rect.left - this.offsetCorrection;
		const startX = this.extractTranslateX(this.gradientTarget.getAttribute('gradientTransform')) || 97;

		if (this.animationFrame) cancelAnimationFrame(this.animationFrame);

		this.animateGradient(startX, offsetX);
	}

	animateGradient(startX, endX, duration = 500) {
		const startTime = performance.now();
		const easeOut = (t) => 1 - Math.pow(1 - t, 3);

		const animate = (time) => {
			const elapsed = time - startTime;
			const progress = Math.min(elapsed / duration, 1);
			const currentX = startX + (endX - startX) * easeOut(progress);

			this.gradientTarget.setAttribute('gradientTransform', `translate(${currentX} 35) rotate(140) scale(82 64)`);

			if (progress < 1) {
				this.animationFrame = requestAnimationFrame(animate);
			} else {
				this.animationFrame = null;
			}
		};

		this.animationFrame = requestAnimationFrame(animate);
	}

	setGradientImmediately(e) {
		const offsetX = e.clientX - this.rect.left - this.offsetCorrection;
		this.gradientTarget.setAttribute('gradientTransform', `translate(${offsetX} 35) rotate(140) scale(82 64)`);
		this.stopAnimation();
	}

	stopAnimation() {
		if (this.animationFrame) {
			cancelAnimationFrame(this.animationFrame);
			this.animationFrame = null;
		}
	}

	extractTranslateX(transform) {
		return parseFloat((/translate\((\d+)/.exec(transform) || [])[1]);
	}

	updateRect() {
		this.rect = this.element.getBoundingClientRect();
	}
}
