import { Controller } from '@hotwired/stimulus';

export const create = () => {
	return class extends Controller {
		static targets = ['inp', 'toolMinus', 'toolPlus'];
		static values = {
			min: Number,
			max: Number,
			limitTooltip: Number,
		};

		connect = () => {
			// Safari fix - programatically changing value to min/max value does not trigger change event afterwards
			this.inpTarget.addEventListener('keydown', () => (this.oldValue = this.inpTarget.value));
		};

		changeValue(event) {
			const value = parseInt(this.inpTarget.value) + parseInt(event.target.dataset.step);

			// change value
			this.inpTarget.value = value;
			this.activeArrows(value);

			// trigger change
			const evt = document.createEvent('HTMLEvents');
			evt.initEvent('change', false, true);
			this.inpTarget.dispatchEvent(evt);
		}
		activeArrows = (value) => {
			this.toolMinusTarget.classList[this.minValue >= 0 && value <= this.minValue ? 'add' : 'remove']('is-disabled');
			this.toolPlusTarget.classList[this.maxValue >= 0 && value >= this.maxValue ? 'add' : 'remove']('is-disabled');
		};
		checkValue = () => {
			const value = parseInt(this.inpTarget.value) || 0;

			if (value < this.minValue) {
				this.inpTarget.value = this.minValue;

				// Safari fix - programatically changing value to min/max value does not trigger change event afterwards
				if (this.oldValue != this.minValue) {
					this.inpTarget.addEventListener('blur', () => this.triggerChange());
				}
			}

			if (value > this.maxValue) {
				this.inpTarget.value = this.maxValue;

				// Safari fix - programatically changing value to min/max value does not trigger change event afterwards
				if (this.oldValue != this.maxValue) {
					this.inpTarget.addEventListener('blur', () => this.triggerChange());
				}
			}

			this.activeArrows(value);
		};

		triggerChange = () => {
			const evt = document.createEvent('HTMLEvents');
			evt.initEvent('change', false, true);
			this.inpTarget.dispatchEvent(evt);
		};
	};
};
