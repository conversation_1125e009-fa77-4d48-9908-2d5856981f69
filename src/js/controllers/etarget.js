import { Controller } from '@hotwired/stimulus';
import { useClickOutside } from 'stimulus-use';

export const create = () => {
	return class extends Controller {
		static values = {
			preventDefault: <PERSON>olean,
		};
		connect() {
			useClickOutside(this);
		}
		clickOutside(e) {
			if (['is-open'].some((cls) => this.element.classList.contains(cls))) {
				if (this.preventDefaultValue) e.preventDefault();
				this.element.classList.remove('is-open');
				// this.element.setAttribute('aria-expanded', 'false');
			}
		}
	};
};
