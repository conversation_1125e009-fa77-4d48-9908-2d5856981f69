import { Controller } from '@hotwired/stimulus';
import { useIntersection } from 'stimulus-use';

export const create = () => {
	return class extends Controller {
		static targets = ['element', 'fixed'];

		connect() {
			useIntersection(this, { element: this.elementTarget });
		}
		appear() {
			this.fixedTarget.classList.remove('is-visible');
		}
		disappear() {
			this.fixedTarget.classList.add('is-visible');
		}
	};
};
