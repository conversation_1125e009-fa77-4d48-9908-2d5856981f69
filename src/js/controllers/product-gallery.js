import { Controller } from '@hotwired/stimulus';
import { setupDotBtns, generateDotBtns, selectDotBtn, disableDots } from './carousel/dotButtons';
import { MQ } from '../tools/MQ';

export const create = () => {
	return class extends Controller {
		static targets = ['carousel', 'thumbs', 'dots'];

		async connect() {
			this.emblaCarousel = (await import(/* webpackPrefetch: true */ 'embla-carousel')).default;

			this.init();
		}
		init = () => {
			this.element.classList.add('is-initialized');

			const optionsCarousel = {
				loop: false,
				align: 'start',
				containScroll: 'trimSnaps',
				skipSnaps: false,
				speed: 20,
			};
			const optionsThumbs = {
				...optionsCarousel,
				skipSnaps: true,
			};

			this.carousel = this.emblaCarousel(this.carouselTarget.querySelector('.embla__viewport'), optionsCarousel); // Hlavni carousel
			if (MQ('lgDown')) this.thumbs = this.emblaCarousel(this.thumbsTarget.querySelector('.embla__viewport'), optionsThumbs); // Thumbs carousel (pouze mobil)

			const { setActiveSlides, loadNext } = this;

			[this.carousel, this.thumbs].forEach((carousel) => {
				if (carousel) {
					// helper class for pointer-events: none when dragging
					var viewportTarget = carousel.rootNode();
					const onScroll = () => viewportTarget.classList.add('is-dragging');
					carousel.on('pointerDown', () => carousel.on('scroll', onScroll));
					carousel.on('pointerUp', () => {
						carousel.off('scroll', onScroll);
						viewportTarget.classList.remove('is-dragging');
					});

					// load image on next slide
					this.loadNext(carousel);
					this.carousel.on('select', () => loadNext(carousel));

					// active
					this.setActiveState(carousel);
				}
			});

			// active slide
			this.carousel.on('select', () => setActiveSlides(this.carousel));

			// dots
			if (this.hasDotsTarget) {
				var dotsArray = generateDotBtns(this.dotsTarget, this.carousel);
				var setSelectedDotBtn = selectDotBtn(dotsArray, this.carousel);
				var disableAllDots = disableDots(this.dotsTarget, this.carousel);
				setupDotBtns(dotsArray, this.carousel);
				setSelectedDotBtn();
				disableAllDots();
				this.carousel.on('select', setSelectedDotBtn);

				this.dots = this.emblaCarousel(this.dotsTarget.parentNode, optionsThumbs);
			}

			// propojeni carouselu s thumbs
			this.carousel.on('select', (carousel) => {
				var index = carousel.selectedScrollSnap();
				this.setActiveThumb(index);

				// posun dots, pokud neni aktivni viditelna
				if (this.hasDotsTarget && this.dots) {
					if (this.dots.slidesNotInView().includes(parseInt(index))) {
						this.dots.scrollTo(index);
					}
				}
			});
		};

		goTo = (e) => {
			var index = e.target.dataset.slide;
			this.carousel.scrollTo(index);
		};

		setActiveThumb = (index) => {
			var thumbSlides = this.thumbsTarget.querySelectorAll('.grid__cell');
			thumbSlides.forEach((slide, i) => {
				slide.classList[i == index ? 'add' : 'remove']('is-active');
			});

			// posun thumb carouselu, pokud neni aktivni prvek viditelny
			if (this.thumbs) {
				if (this.thumbs.slidesNotInView().includes(index)) {
					this.thumbs.scrollTo(index);
				}
			}
		};

		loadNext = (carousel) => {
			carousel.slideNodes()[carousel.selectedScrollSnap() + 1]?.querySelector('[loading="lazy"]')?.removeAttribute('loading');
		};

		setActiveSlides = (carousel) => {
			carousel
				.slideNodes()
				.forEach((slide, i) => slide.classList[this.carousel.selectedScrollSnap() == i ? 'add' : 'remove']('is-active'));
		};

		setActiveState = (carousel) => {
			var el = carousel.rootNode().closest('.embla');
			this.isScrollable = carousel.internalEngine().scrollSnaps.length > 1;
			carousel.reInit({ active: this.isScrollable });
			el.classList[this.isScrollable ? 'remove' : 'add']('is-disabled');
		};
	};
};
