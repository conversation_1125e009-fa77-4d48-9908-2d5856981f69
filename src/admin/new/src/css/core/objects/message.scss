.message {
	display: flex;
	align-items: center;
	padding: $sm;
	border-radius: 2px;
	background: $colorBg;
	&__icon {
		width: 24px;
		height: 24px;
		margin-right: $xs;
	}
	&__content {
		> :last-child {
			margin-bottom: 0;
		}
	}

	// VARIANTs
	&--error,
	&--ok,
	&--warning {
		color: $colorWhite;
		li {
			&::before {
				background-color: $colorWhite;
			}
		}
		a {
			color: $colorWhite;
		}
	}
	&--error {
		background: $colorRed;
	}
	&--ok {
		background: $colorGreen;
	}
	&--warning {
		background: $colorOrange;
	}
}
