{% include '@components/parts/list-item.twig' with {
	props: {
		dragdrop: true,
		inps: [
			{
				id: '[pages][newItemMarker][name]',
				placeholder: '<PERSON><PERSON><PERSON><PERSON> n<PERSON> str<PERSON>'
			}
		],
		btnsAfter: [
			{
				icon: '@icons/trash.svg',
				tooltip: 'Odstranit',
				variant: 'remove',
				data: {
					'action': 'RemoveItem#remove'
				},
			}
		],
		data: {
			'controller': 'RemoveItem',
			'RemoveItem-target': 'item',
		},
	}
} %}
