{% include '@components/box/list.twig' with {
	props: {
		title: 'Modi<PERSON><PERSON><PERSON>',
		items: [
			{
				btnsBefore: [
					{
						classes: ['is-active'],
						icon: '@icons/eye.svg',
						type: 'checkbox',
						data: {
							'controller': 'Toggle',
							'action': 'Toggle#changeClass',
							'toggle-target-value': 'body',
							'toggle-target-class-value': 'lang-cz',
							'toggle-class-value': 'is-active'
						},
						tooltip: 'Zobrazit/Skrýt'
					}
				],
				texts: [
					{
						text: 'Česky'
					}
				],
				tags: [
					{
						text: 'cz'
					}
				],
				btnsAfter: [
					{
						classes: ['btn-icon--disabled'],
						icon: '@icons/trash.svg'
					}
				]
			},
			{
				btnsBefore: [
					{
						icon: '@icons/eye.svg',
						type: 'checkbox',
						data: {
							'controller': 'Toggle',
							'action': 'Toggle#changeClass',
							'toggle-target-value': 'body',
							'toggle-target-class-value': 'lang-en',
							'toggle-class-value': 'is-active'
						},
						tooltip: 'Zobrazit/Skrýt'
					}
				],
				texts: [
					{
						text: 'English'
					}
				],
				tags: [
					{
						text: 'en'
					}
				],
				btnsAfter: [
					{
						icon: '@icons/trash.svg',
						tooltip: 'Odstranit',
						variant: 'remove',
					}
				]
			},
			{
				btnsBefore: [
					{
						classes: ['btn-icon--disabled'],
						icon: '@icons/eye.svg',
						type: 'checkbox'
					}
				],
				texts: [
					{
						text: 'Deutch'
					}
				],
				tags: [
					{
						text: 'de'
					}
				],
				btnsAfter: [
					{
						icon: '@icons/plus.svg',
						tooltip: 'Přidat'
					}
				]
			}
		]
	}
} %}
