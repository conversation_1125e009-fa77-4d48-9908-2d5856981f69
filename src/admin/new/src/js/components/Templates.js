import $ from 'jquery';
import { Controller } from 'stimulus';
import { useDispatch } from 'stimulus-use';

export default class Templates extends Controller {
	static targets = ['overlays'];

	templateTargets = {};

	connect() {
		const templates = Array.from(this.element.querySelectorAll('[data-Templates-target$="Item"], [data-Templates-target$="Overlay"]'));

		this.templateTargets = templates.reduce((acc, item) => {
			const key = item.dataset.templatesTarget;

			if (!key) return acc;

			if (this.templateTargets[key]) {
				this.templateTargets[key].push(item);
			} else {
				this.templateTargets[key] = [item];
			}

			return acc;
		}, this.templateTargets);
		useDispatch(this);
	}

	getImageListItem(node, id, src, uploadUrl, imageName) {
		if (node) {
			return $(
				node.outerHTML
					.replace(/\{imageId\}/gm, id)
					// eslint-disable-next-line no-useless-escape
					.replace(/data\-src=\"\{imageSrc\}\"/gm, `data-src="${src}" src="${src}"`)
					.replace(/\{uploadUrl\}/gm, uploadUrl)
					.replace(/\{imageName\}/gm, imageName),
			).get(0);
		} else {
			return null;
		}
	}

	getListItem(itemTargets, mutationId, templateId) {
		const currentTarget = mutationId !== '' ? itemTargets.find((item) => item.dataset.mutationid === mutationId) : itemTargets[0];
		if (currentTarget) {
			return $(currentTarget.innerHTML.replace(/newItemMarker/gm, templateId)).get(0);
		} else {
			return null;
		}
	}

	newItem(e) {
		const { name, id, listTarget, mutationId, file, src, uploadUrl, alt } = e.detail;
		const templateId = `newItemMarker_${id}`;
		let listItem = null;
		let overlay = null;

		const hasItemTarget = Boolean(this.templateTargets[`${name}Item`]);
		const hasOverlayTarget = Boolean(this.templateTargets[`${name}Overlay`]);

		switch (name) {
			case 'variant':
				if (hasItemTarget) {
					listItem = this.getListItem(this.templateTargets[`${name}Item`], mutationId, templateId);
				}
				if (hasOverlayTarget) {
					overlay = $(this.templateTargets[`${name}Overlay`][0].innerHTML.replace(/newItemMarker/gm, templateId)).get(0);
				}

				break;
			case 'price':
				if (hasItemTarget) {
					listItem = this.getListItem(this.templateTargets[`${name}Item`], mutationId, templateId);
				}
				if (hasOverlayTarget) {
					overlay = $(this.templateTargets[`${name}Overlay`][0].innerHTML.replace(/newItemMarker/gm, templateId)).get(0);
				}

				break;

			case 'image':
				if (hasItemTarget) {
					const imageId = id.split('image_')[1];
					listItem = this.getImageListItem(
						this.getListItem(this.templateTargets[`${name}Item`], mutationId, templateId),
						imageId,
						src,
					);
					const idInput = listItem.querySelector('[data-image-id]');
					if (idInput) {
						idInput.value = imageId;
					}
				}
				if (hasOverlayTarget) {
					overlay = $(
						this.templateTargets[`${name}Overlay`][0].innerHTML
							.replace(/newItemMarker/gm, templateId)
							.replace(/\{imageAlt\}/gm, alt || ''),
					).get(0);
				}

				break;

			case 'libraryImage':
				if (hasItemTarget) {
					const imageId = id.split('image_')[1];
					listItem = this.getImageListItem(
						this.getListItem(this.templateTargets[`${name}Item`], mutationId, templateId),
						imageId,
						src,
						uploadUrl,
						file.name,
					);
				}

				break;

			default:
				if (hasItemTarget) {
					listItem = this.getListItem(this.templateTargets[`${name}Item`], mutationId, templateId);
				}

				break;
		}

		if (listItem && listTarget) {
			if (name === 'libraryImage') {
				listTarget.prepend(listItem);
			} else {
				listTarget.appendChild(listItem);
			}
			if (name === 'file' || name === 'libraryImage') {
				setTimeout(() => {
					this.dispatch('uploadFile', { id: templateId, file });
				}, 0);
			}

			// focus text input if there is any
			const textInput = listItem.querySelector('input[type="text"]');
			if (textInput) {
				setTimeout(() => {
					textInput.focus();
				}, 0);
			}
		}

		if (overlay && this.hasOverlaysTarget) {
			this.overlaysTarget.appendChild(overlay);
			this.dispatch('edit', { id: templateId });
		}
	}

	remove(e) {
		const { id } = e.detail;

		if (id) {
			const overlayToDelete = document.getElementById(`overlay-${id}`) || document.getElementById(`overlay-newItemMarker_${id}`);
			if (overlayToDelete) {
				overlayToDelete.remove();
			}
		}
	}
}
