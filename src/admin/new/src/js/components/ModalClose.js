import { Controller } from 'stimulus';
import { useHotkeys } from 'stimulus-use';

export default class ModalClose extends Controller {
	connect() {
		useHotkeys(this, {
			Escape: [this.removeClass],
		});
	}

	removeClass() {
		var elements = Array.from(document.querySelectorAll('.b-overlay.is-visible .b-header__back'));

		elements.forEach((element) => {
			if (!element.getAttribute('href').includes('newItemMarker')) {
				element.click();
			}
		});
	}
}
