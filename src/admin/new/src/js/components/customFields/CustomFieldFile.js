import { nanoid } from 'nanoid';
import { ApplicationController } from 'stimulus-use';
import { fileItem } from './templates/file';

export default class CustomFieldFile extends ApplicationController {
	static targets = ['content', 'file', 'add'];
	static values = {
		key: String,
		multiple: Boolean,
		uploadurl: String,
		ids: Array,
	};

	add(id, file) {
		if (this.hasContentTarget) {
			const fileEl = document.createElement('div');
			this.contentTarget.append(fileEl);
			fileEl.outerHTML = fileItem(id, { name: file.name }, this.uploadurlValue, true);
			setTimeout(() => {
				this.dispatch('uploadFile', { id, file });
			}, 0);
		}

		this.idsValue = [...this.idsValue, id];

		if (!this.multipleValue && this.hasAddTarget && this.idsValue.length > 0) {
			this.addTarget.classList.add('u-hide');
		}
	}

	remove(fileId) {
		this.idsValue = this.idsValue.filter((id) => id !== fileId);
		this.updateFilesValue();

		if (!this.multipleValue && this.hasAddTarget && this.idsValue.length === 0) {
			this.addTarget.classList.remove('u-hide');
		}
	}

	addFile(e) {
		if (e.target.files) {
			Array.from(e.target.files).forEach((file) => {
				this.add(nanoid(), file);
			});
			// clear the file list
			e.target.type = 'text';
			e.target.type = 'file';
		}
	}

	removeFile(e) {
		const item = e.target.closest('[data-id]');

		if (!item) return;

		this.remove(item.dataset.id);
	}

	fileUploaded(e) {
		const { id, oldId } = e.detail;
		this.idsValue = this.idsValue.map((currentId) => (currentId === oldId ? id : currentId));
		this.updateFilesValue();
	}

	updateFilesValue() {
		const currentFiles = this.fileTargets.filter((fileEl) => this.idsValue.includes(fileEl.dataset.id));
		const newValue = currentFiles.map((fileEl) => {
			const fileNameInp = fileEl.querySelector('[data-file-target="nameInput"]');
			const fileIdInp = fileEl.querySelector('[data-file-target="fileId"]');

			return {
				id: fileIdInp?.value || '',
				name: fileNameInp?.value || '',
			};
		});

		this.dispatch('updateValue', { value: newValue, key: this.keyValue });
	}
}
