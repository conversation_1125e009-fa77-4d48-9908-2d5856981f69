export const dateTime = (parent, field, value, scheme) => {
	const { id: key } = field;
	const tplElement = document.createElement('div');
	const keyChunks = key.split('-');
	keyChunks.pop();
	parent.append(tplElement);
	tplElement.outerHTML = `

<div class="inp u-mb-sm" data-controller="CustomField" data-customfield-key-value='${key}'>
	${scheme.label ? `<label for="custom-${key}" class="inp-label title">${scheme.label}</label>` : ''}
	<div class="inp-fix u-mb-sm">
		<input
			name="custom-${key}"
			id="custom-${key}"
			class="inp-text"
			type="datetime-local"
			value="${value}"
			data-action="change->CustomField#updateValue"
		>
		<div class="inp-text__holder"></div>
	</div>
</div>
`;
};
