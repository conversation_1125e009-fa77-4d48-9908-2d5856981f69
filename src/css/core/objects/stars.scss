@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.stars {
	--icon-size: 1.4rem;
	--gap: 0rem;
	--active-color: #{variables.$color-yellow-600};
	--unactive-color: #{variables.$color-tile};
	display: flex;
	gap: 0.4rem;
	align-items: center;
	line-height: 1;
	&__inner {
		display: inline-flex;
		gap: 1rem;
		justify-content: center;
		align-items: center;
		line-height: 0;
	}
	&__icons {
		position: relative;
		display: inline-flex;
		margin: 0 calc(var(--gap) * -0.5);
		color: var(--unactive-color);
		.icon-svg {
			flex: 0 0 auto;
			width: var(--icon-size);
			height: var(--icon-size);
			margin: 0 calc(var(--gap) / 2);
		}
	}
	&__rating {
		flex: 0 0 auto;
		padding-top: 0.15em;
	}

	// MODIF
	&__icons--active {
		position: absolute;
		top: 0;
		left: 0;
		margin: 0;
		color: var(--active-color);
		white-space: nowrap;
		overflow: hidden;
	}

	// MQ
	@media (config.$md-up) {
		--icon-size: 1.8rem;
		gap: 0.8rem;
	}
}
