@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.status {
	display: inline-block;
	padding: 0.5rem 0.8rem;
	border-radius: variables.$border-radius-sm;
	background: variables.$color-primary-150-light;
	color: variables.$color-primary-150;
	font-family: variables.$font-secondary;
	font-weight: 600;
	font-size: 1.1rem;
	line-height: normal;
	text-transform: uppercase;

	// MODIF
	&--success {
		background: variables.$color-green-alert;
		color: variables.$color-green;
	}
	&--warning {
		background: variables.$color-orange-light-2;
		color: variables.$color-orange;
	}
	&--error {
		background: variables.$color-red-light;
		color: variables.$color-red;
	}
}
