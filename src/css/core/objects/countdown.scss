@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.countdown {
	$s: &;
	position: relative;
	margin: 0 0 1.6rem;
	padding: 1.1rem 2rem;
	border-radius: variables.$border-radius-md;
	background: rgba(variables.$color-black, 0.8);
	color: variables.$color-white;
	font-size: 1.3rem;
	text-align: center;
	&::before {
		content: '';
		position: absolute;
		top: 100%;
		left: 5rem;
		width: 0;
		height: 0;
		border-width: 0.5rem 0.6rem 0;
		border-style: solid;
		border-color: rgba(variables.$color-black, 0.8) transparent transparent;
	}

	// MODIF
	// bez šipky - je napozicovaná na základě u price__discount elementu
	&:has(+ * .price__discount)::before {
		display: none;
	}
	&--detail {
		min-width: 22rem;
		margin: 0 0 0.8rem;
		padding: 0.2rem 0.8rem 0.1rem;
		font-size: 1.2rem;
		&::before {
			left: 50%;
			transform: translateX(-50%);
		}
	}
}
