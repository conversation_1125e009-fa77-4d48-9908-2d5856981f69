@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-heureka-rating {
	$s: &;
	&__list {
		@extend %reset-ul;
	}
	&__item {
		@extend %reset-ul-li;
	}
	&__stars {
		--icon-size: 1.6rem;
	}
	&__verified {
		--icon-size: 1.5rem;
		--gap: 0.4rem;
	}

	// MODIF
	&__list--plus,
	&__list--minus {
		#{$s}__item {
			position: relative;
			margin: 0 0 0.4rem;
			padding: 0 0 0 2rem;
			&::before,
			&::after {
				content: '';
				position: absolute;
				top: 1.2rem;
				left: 0;
				width: 1.2rem;
				height: 0.2rem;
				border-radius: 0.2rem;
				background: variables.$color-status-valid;
			}
			&::after {
				transform: rotate(-90deg);
			}
		}
	}
	&__list--minus &__item {
		&::before {
			background: variables.$color-status-invalid;
		}
		&::after {
			content: none;
		}
	}
}
