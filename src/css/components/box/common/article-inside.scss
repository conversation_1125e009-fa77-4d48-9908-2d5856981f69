@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-article-inside {
	$s: &;
	position: relative;
	border-radius: variables.$border-radius-lg;
	overflow: hidden;
	aspect-ratio: 16/9;
	&::before {
		content: '';
		position: absolute;
		z-index: 1;
		background: linear-gradient(180deg, rgba(1, 4, 20, 0) 29.1%, rgba(1, 4, 20, 0.72) 80.2%);
		inset: 0;
	}
	&__bg {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
	&__play {
		position: static;
		width: 3.1rem;
		transform: none;
	}
	&__content {
		position: relative;
		z-index: 1;
		display: flex;
		flex-direction: column;
		height: 100%;
		padding: 1.6rem;
		color: variables.$color-white;
	}
	&__bottom {
		display: flex;
		gap: 2rem;
		align-items: flex-end;
		margin-top: auto;
	}
	&__main {
		max-width: 57.5rem;
	}
	&__info {
		display: flex;
		gap: 0.8rem;
		flex-wrap: wrap;
		align-items: center;
		margin: 0 0 0.8rem;
	}
	&__flags {
		@extend %reset-ul;
		display: flex;
		gap: 0.4rem;
		flex-wrap: wrap;
		li {
			@extend %reset-ul-li;
			display: flex;
		}
		.flag {
			--flag-h: 1.9rem;
			--flag-fs: 0.8rem;
		}
	}
	&__published {
		margin: 0;
		color: variables.$color-inverse-help;
		font-size: 1rem;
	}
	&__author {
		flex: 0 0 auto;
		margin: 0 0 0 auto;
		.author {
			--author-img-size: 2.4rem;
			--author-fs: 1rem;
			--author-gap: 0rem;
		}
	}
	&__title {
		--font-size-mobile: 1.6rem;
		--font-size-desktop: 1.6rem;
		margin: 0;
		color: inherit;
	}
	&__link {
		--color-link: #{variables.$color-white};
		--color-hover: var(--color-link);
		--color-link-decoration: transparent;
		@include mixins.line-clamp(2);
	}

	// MQ
	@media (config.$md-up) {
		&__content {
			padding: 2rem;
		}

		&__flags {
			.flag {
				--flag-h: 2.2rem;
				--flag-fs: 1.1rem;
			}
		}
		&__published {
			font-size: 1.1rem;
		}
		&__author .author {
			--author-img-size: 3.4rem;
			--author-fs: 1.2rem;
			--author-gap: 0.4rem;
		}

		// MODIF
		&--lg {
			#{$s} {
				&__content {
					padding: 3.6rem;
				}
				&__play {
					width: 6.5rem;
				}
				&__published {
					font-size: 1.2rem;
				}
				&__title {
					--font-size-mobile: 2rem;
					--font-size-desktop: 3rem;
				}
			}
		}
	}
}
