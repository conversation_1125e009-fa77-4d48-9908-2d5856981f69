@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-mention {
	display: flex;
	flex-direction: column;
	.grid__cell > & {
		height: 100%;
	}
	&__img-holder {
		position: relative;
		border-radius: variables.$border-radius-lg variables.$border-radius-lg 0 0;
		overflow: hidden;
	}
	&__play {
		position: absolute;
		top: 1.5rem;
		left: 1.5rem;
		width: 3.1rem;
		transform: none;
	}
	&__flags {
		@extend %reset-ul;
		position: absolute;
		bottom: 1.5rem;
		left: 1.5rem;
		display: flex;
		gap: 0.4rem;
		flex-wrap: wrap;
		li {
			@extend %reset-ul-li;
			display: flex;
		}
		.flag {
			--flag-h: 2.2rem;
			--flag-fs: 1.1rem;
		}
	}
	&__content {
		display: flex;
		flex: 1;
		flex-direction: column;
		padding: 2rem;
		border: 0.1rem solid variables.$color-tile;
		border-width: 0 0.1rem 0.1rem;
		border-radius: 0 0 variables.$border-radius-lg variables.$border-radius-lg;
	}
	&__title {
		margin: 0 0 0.8rem;
	}
	&__link {
		--color-hover: var(--color-link);
		--color-link-decoration: transparent;
		@include mixins.line-clamp(2);
	}
	&__bottom {
		display: flex;
		align-items: center;
		margin-top: auto;
	}
	&__logos {
		.img {
			width: 8.9rem;
		}
	}
	&__date {
		margin: 0 0 0 auto;
		color: variables.$color-help;
		font-size: 1.3rem;
		line-height: 1.4;
	}

	// MQ
	@media (config.$md-up) {
		&__content {
			padding: 2.4rem 3.2rem;
		}
	}
}
