@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-annot {
	$s: &;
	position: relative;
	margin: 0 0 2rem;
	padding-top: 1.6rem;
	&__title {
		margin: 0 0 1.2rem;
	}
	&__annot p {
		margin: 0 0 0.625em;
	}
	&__decor {
		position: absolute;
		top: 0;
		right: calc(-3.5rem - var(--row-main-gutter));
		display: block;
		width: 13rem;
		max-width: none;
	}

	// MQ
	@media (config.$md-up) {
		margin: 0 0 3.2rem;
		padding-top: 5.2rem;
		&__title {
			margin: 0 0 1.6rem;
		}
		&__decor {
			right: calc(-5.5rem - var(--row-main-gutter));
			width: 20rem;
		}
	}
	@media (config.$xl-up) {
		&__decor {
			right: 50%;
			width: 46rem;
			margin-right: calc(var(--vw) / -2);
		}
		&:has(&__decor) {
			padding-right: 48rem;
		}
	}
}
