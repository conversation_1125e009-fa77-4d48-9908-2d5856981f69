@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-payment-instructions {
	&__copy.item-icon {
		--icon-color: #{variables.$color-primary};
		--color-link: #{variables.$color-text};
		--color-hover: #{variables.$color-primary};
		--icon-size: 1.5rem;
		--gap: 0.4rem;
		position: relative;
		text-decoration: none;
		.item-icon__icon {
			transition: opacity variables.$t;
		}
		.item-icon__icon:nth-child(3) {
			position: absolute;
			top: 50%;
			right: 0;
			opacity: 0;
			transform: translateY(-50%);
		}
	}

	// STATES
	&__copy.is-copied .item-icon__icon:nth-child(2) {
		opacity: 0;
	}
	&__copy.is-copied .item-icon__icon:nth-child(3) {
		opacity: 1;
	}
}
