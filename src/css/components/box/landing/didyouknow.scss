@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-didyouknow {
	display: flex;
	align-items: center;
	margin: 0 0 3.2rem;
	font-weight: bold;
	font-size: 1rem;
	&__img {
		flex: 0 0 auto;
		width: 8.5rem;
		margin: 0 -3rem 0 0;
		img {
			aspect-ratio: 16/13;
			object-fit: contain;
		}
	}
	&__box {
		display: flex;
		flex: 1;
		flex-direction: column;
		padding: 1.3rem 1.6rem;
		border-radius: variables.$border-radius-md;
		background-color: rgba(variables.$color-bg, 0.85);
		backdrop-filter: blur(2rem);
		& > * {
			margin: 0;
		}
	}
	&__title {
		font-size: 1.3rem;
	}
	&__more {
		font-size: 1.2rem;
		.item-icon {
			--icon-size: 1.5rem;
			--icon-gap: 0.8rem;
			--color-link-decoration: transparent;
			--color-hover: var(--color-link);
		}
	}

	// MQ
	@media (config.$md-up) {
		margin: 0 0 8rem;
		font-size: 1.2rem;
		&__img {
			width: 17.2rem;
			margin: 0 -4rem 0 0;
		}
		&__box {
			gap: 0.4rem;
			padding: 2.2rem 3.2rem;
			border-radius: variables.$border-radius-xl;
		}
		&__title {
			font-size: 1.8rem;
		}
		&__more {
			font-size: 1.4rem;
		}
	}
}
