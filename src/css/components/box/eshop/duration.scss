@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-duration {
	display: flex;
	gap: 0.8rem;
	align-items: center;
	margin: 0 0 1.6rem;
	padding: 1.2rem 1.6rem;
	border-radius: variables.$border-radius-lg;
	background: variables.$color-bg;
	font-size: 1.3rem;
	&__icon {
		flex: 0 0 auto;
		width: 4rem;
	}

	// MQ
	@media (config.$md-up) {
		gap: 1.2rem;
		margin: 0 0 2.4rem;
		padding: 2rem 2.8rem;
		font-size: 1.4rem;
		&__icon {
			width: 4.8rem;
		}
	}
}
