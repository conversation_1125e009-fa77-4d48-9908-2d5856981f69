@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.b-filters {
	gap: 0.4rem;
	font-size: 1.3rem;
	.flag {
		--flag-h: 2.9rem;
	}
	&__list {
		@extend %reset-ul;
	}
	&__item {
		@extend %reset-ul-li;
	}

	// MQ
	@media (config.$md-down) {
		&__list {
			display: flex;
			gap: 0.8rem;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			& & {
				gap: 0.4rem;
				flex-direction: row;
				flex-wrap: wrap;
			}
		}
		&__name {
			display: inline-block;
			margin: 0 0 0.4rem;
		}
	}
	@media (config.$md-up) {
		display: flex;
		flex-wrap: wrap;
		justify-content: center;
		align-items: center;
		&__list,
		&__item {
			display: contents;
		}
		&__name,
		&__reset {
			padding-left: 1.2rem;
		}
	}
	@media (config.$lg-up) {
		position: relative;
		padding: 1.2rem 3.2rem;
		&::before {
			content: '';
			position: absolute;
			top: 0;
			right: 0;
			left: 0;
			height: 0.1rem;
			background: variables.$color-tile;
		}
		@media (config.$xl-down) {
			&:first-child {
				margin-top: 1.2rem;
			}
		}
	}
	@media (config.$xl-up) {
		border-radius: 0 0 variables.$border-radius-xl variables.$border-radius-xl;
		background: variables.$color-bg;
		&::before {
			right: 3.2rem;
			left: 3.2rem;
		}
	}
}
