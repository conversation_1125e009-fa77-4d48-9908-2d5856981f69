@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-total {
	padding: 1.6rem;
	border-radius: variables.$border-radius-md;
	background: variables.$color-bg;
	&__table {
		--table-y-padding: 0rem;
		--table-x-padding: 3.4rem;
		margin: 0;
		td {
			border: none;
		}
		td:last-child {
			text-align: right;
		}
		tbody {
			font-size: 1.3rem;
		}
		tfoot {
			color: variables.$color-black;
			font-family: variables.$font-secondary;
			font-size: 1.4rem;
		}
		tfoot td {
			padding-top: 0.4rem;
		}
	}

	// MQ
	@media (config.$md-up) {
		padding: 2rem 4rem;
		border-radius: variables.$border-radius-xl;
		&__table {
			--table-y-padding: 0.2rem;
			--table-x-padding: 3.4rem;
			width: auto;
			margin: 0 0 0 auto;
			tbody {
				font-size: 1.4rem;
			}
			tfoot {
				font-size: 1.6rem;
			}
			tfoot td {
				padding-top: 1rem;
			}
		}
	}
}
