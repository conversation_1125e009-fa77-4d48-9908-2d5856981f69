@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-extra {
	$s: &;
	display: grid;
	grid-template-columns: subgrid;
	grid-column: auto / span 5;
	align-items: center;
	margin: 0 0 1.5rem;
	padding: 1.6rem 0 0;
	&:last-child {
		margin-bottom: 0;
	}
	& + & {
		padding-top: 0;
	}
	&__name {
		--name-bg: #{variables.$color-bg};
		--name-color: #{variables.$color-help};
		grid-column: auto / span 5;
		margin: 0 0 0.8rem;
		padding: 0.8rem 1.2rem;
		border-radius: variables.$border-radius-md;
		background: var(--name-bg);
		color: var(--name-color);
		font-weight: bold;
		font-size: 1.2rem;
	}
	&__radios {
		grid-column: auto / span 5;
		padding: 1.2rem 0 0;
	}
	&__radio {
		--inp-item-fs: 1.3rem;
		display: flex;
		padding: 0.8rem 1.2rem 0.8rem 4rem;
		border-radius: variables.$border-radius-lg;
		transition: background-color variables.$t;
		.inp-item__text::before {
			top: 0.5rem;
			left: 0.8rem;
		}
		.inp-item__text::after {
			top: 1.05rem;
			left: 1.35rem;
		}
	}
	&__services {
		display: grid;
		grid-template-columns: subgrid;
		grid-column: auto / span 5;
		margin: 0;
	}
	&__checkbox {
		display: contents;
		.inp-item__text {
			position: relative;
			display: flex;
			grid-column: auto / span 5;
			gap: 1.2rem;
			padding: 0.6rem 1.2rem 0.6rem 4rem;
			border-radius: variables.$border-radius-lg;
			transition: background-color variables.$t;
		}
		.inp-item__text::before {
			top: 0.5rem;
			left: 0.4rem;
		}
		.inp-item__text::after {
			top: 1rem;
			left: 1.3rem;
		}
		a {
			font-weight: 400;
		}
	}
	&__service-name {
		position: relative;
		display: inline-block;
		padding-right: 4rem;
	}
	&__tooltip {
		padding-left: 1.2rem;
		border-left: 0.1rem solid variables.$color-tile;
	}
	&__service-name &__tooltip {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
	}
	&__variant {
		display: block;
		color: variables.$color-help;
		font-weight: 400;
		font-size: 1.2rem;
	}

	// STATES
	&__radio:has(input:checked),
	&__checkbox:has(input:checked) .inp-item__text {
		font-weight: bold;
	}
	&__checkbox:not(:has(input:checked)) .b-cart__price {
		color: variables.$color-help;
	}

	// MODIF
	&--pick-gift,
	&--on-request {
		font-size: 1.3rem;
		line-height: 1.6;
		.b-cart__content p {
			margin-bottom: 0.8rem;
		}
	}
	&--on-request {
		.b-cart__content .item-icon {
			margin-bottom: 0.8rem;
		}
	}
	&__name--error {
		--name-bg: #{variables.$color-status-invalid-light};
		--name-color: #{variables.$color-status-invalid};
	}
	&__name--arrow {
		position: relative;
		&::before {
			content: '';
			position: absolute;
			top: -0.3rem;
			left: 1.5rem;
			width: 1rem;
			height: 1rem;
			background: var(--name-bg);
			transform: rotate(45deg);
		}
	}

	// HOVERS
	.hoverevents &__radio:hover,
	.hoverevents &__checkbox:hover .inp-item__text {
		background: #f8f9ff;
	}

	// MQ
	@container (max-width: 879px) {
		&__checkbox {
			.b-cart__content {
				flex: 1;
			}
			.b-cart__price {
				flex: 0 0 auto;
				width: 6.5rem;
			}
		}

		// MODIF
		&--services {
			.b-cart__img {
				display: none;
			}
		}
		&--pick-gift,
		&--on-request {
			.b-cart__img {
				display: none;
			}
			.b-cart__content {
				grid-column: auto / span 5;
				padding: 0 0.8rem;
			}
		}
		&--services .inp-item__text {
			font-size: 1.3rem;
		}
	}
	@container (min-width: 880px) {
		grid-column: auto / span 6;
		margin: 0 0 3.2rem;
		padding: 2.2rem 0 0;
		&:last-child {
			margin-bottom: 1rem;
		}
		&__name {
			grid-column: 2 / span 5;
			font-size: 1.4rem;
		}
		&__radios {
			grid-area: 3/2/3/7;
			padding: 1.4rem 0 0 1.2rem;
		}
		&__radio {
			--inp-item-fs: 1.4rem;
			padding: 0.6rem 1.2rem 0.6rem 4.8rem;
			.inp-item__text::before {
				left: 1.2rem;
			}
			.inp-item__text::after {
				left: 1.75rem;
			}
		}
		&__services {
			grid-column: 2 / span 5;
		}
		&__checkbox {
			.inp-item__text {
				display: grid;
				grid-template-columns: subgrid;
				gap: 0;
				padding: 0.6rem 1.2rem 0.6rem 4.8rem;
			}
			.inp-item__text::before {
				left: 1.2rem;
			}
			.inp-item__text::after {
				left: 2.1rem;
			}
		}
		&__variant {
			font-size: 1.3rem;
		}

		// MODIF
		&--pick-gift,
		&--on-request {
			font-size: 1.4rem;
			.b-cart__content {
				grid-area: auto / span 2;
			}
		}
		&--pick-gift {
			#{$s}__name,
			.b-cart__content p {
				margin-bottom: 2rem;
			}
		}
		&--on-request {
			#{$s}__name,
			.b-cart__content p {
				margin-bottom: 1.2rem;
			}
		}
	}
}
