@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-cart {
	container-type: inline-size;
	&__body {
		display: grid;
		grid-template-columns: min-content auto 1fr auto auto;
	}
	&__row {
		display: grid;
		grid-template-columns: subgrid;
		grid-column: auto / span 5;
		gap: 0 0.5rem;
		align-items: center;
		margin: 0 0 -0.1rem;
		padding: 1.2rem;
		border: 0.1rem solid variables.$color-tile;
		border-radius: variables.$border-radius-md;
		&:last-child {
			margin: 0;
		}
		& > *:not(.b-extra) {
			padding: 0;
		}
		& > * {
			border: none;
		}
	}
	&__img {
		position: relative;
		width: 7.2rem;
		margin: 0 0.7rem 0 0;
		img {
			border-radius: variables.$border-radius-sm;
		}
	}
	&__content {
		.item-icon {
			--icon-size: 1.5rem;
			--gap: 0.6rem;
			display: flex;
		}
	}
	&__link {
		--color-link-decoration: transparent;
		--color-hover: var(--color-link);
	}
	&__variant {
		color: variables.$color-help;
		font-size: 1.2rem;
	}
	&__availability {
		margin: 0;
	}
	&__price-unit {
		font-size: 1.3rem;
	}
	&__count {
		text-align: center;
	}
	&__price {
		color: variables.$color-black;
		text-align: right;
	}
	&__more {
		display: flex;
	}

	// MQ
	@container (max-width: 879px) {
		font-size: 1.4rem;
		&__img {
			align-self: flex-start;
			margin-bottom: 0.8rem;
		}
		&__price-unit {
			grid-area: auto / span 2;
		}
		&__content {
			grid-area: auto / span 4;
		}
		&__row > &__content {
			margin-bottom: 0.8rem;
			&:last-child {
				margin: 0;
			}
		}
		&__btn .btn {
			--btn-padding: 0.7rem 1rem 0.5rem;
		}
	}
	@container (min-width: 880px) {
		&__table {
			--cart-gap: 4rem;
			--cart-gap-img: 2.4rem;
		}
		&__body {
			grid-template-columns: min-content 1fr auto auto auto auto;
		}
		&__row {
			grid-column: auto / span 6;
			gap: 0 var(--cart-gap);
			padding: 1.2rem 2rem;
			border-radius: variables.$border-radius-xl;
		}
		&__img {
			width: 12.8rem;
			margin: 0 calc((var(--cart-gap) - var(--cart-gap-img)) * -1) 0 0;
			img {
				border-radius: variables.$border-radius-md;
			}
		}
		&__variant {
			font-size: 1.4rem;
		}
		&__availability {
			margin: 0.4rem 0 0;
		}
		&__price-unit {
			font-size: 1.5rem;
			text-align: right;
		}
		&__more {
			margin-right: -0.8rem;
		}

		// MODIF
		&__extra > &__content {
			padding-left: 1.2rem;
		}
		&__extra:has(.b-extra__radios) &__img {
			grid-row: auto / span 2;
			align-self: flex-start;
		}
		.b-extra__checkbox &__content {
			grid-column: auto / span 3;
		}
	}
}
