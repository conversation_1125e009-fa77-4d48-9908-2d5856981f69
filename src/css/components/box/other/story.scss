@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/typography';

.b-story {
	--arrow-position: -0.5rem;
	&__content {
		position: relative;
		&::before {
			content: '';
			position: absolute;
			top: 0;
			right: 0;
			width: 4rem;
			height: 4rem;
			background: url(variables.$img-path + '/bg/quote.svg');
			background-position: center center;
			background-repeat: no-repeat;
			background-size: cover;
		}
		h2,
		h3 {
			@extend %h4;
			margin: 0 0 0.5em;
		}
	}
	&__photos > img {
		width: auto;
		height: 19.8rem;
		border-radius: variables.$border-radius-sm;
		&:last-child {
			margin-right: 0.8rem;
		}
	}

	// MQ
	@media (config.$md-down) {
		position: relative;
		&__main {
			margin: 0 0 0.8rem;
			padding: 2rem 2rem 2.4rem;
		}
		&__photos-holder {
			position: relative;
			left: 50%;
			width: var(--vw, 100vw);
			margin-left: calc(var(--vw, 100vw) * -0.5);
			overflow: hidden;
		}
		&__photos {
			display: flex;
			gap: 0.8rem;
		}
		.embla__btn {
			top: auto;
			bottom: 9rem;
			transform: none;
		}
	}
	@media (config.$md-up) {
		--arrow-position: 1rem;
		/* stylelint-disable-next-line prettier/prettier */
		--content-width: #{calc((variables.$row-main-width - variables.$row-main-gutter) / variables.$grid-columns * 8 - variables.$row-main-gutter)};
		position: relative;
		left: 50%;
		width: var(--vw, 100vw);
		margin-left: calc(var(--vw, 100vw) * -0.5);
		&__holder {
			margin-bottom: -1rem;
			overflow: hidden;
		}
		&__items {
			display: flex;
			gap: 2rem;
			padding-bottom: 1rem;
		}
		&__main {
			display: flex;
			flex: 0 0 auto;
			flex-direction: column;
			justify-content: center;
			width: calc(100% - 4rem);
			max-width: var(--content-width);
			padding: 5rem 10rem;
		}
		&__content {
			font-size: 1.8rem;
			line-height: calc(26 / 18);
			letter-spacing: -0.035em;
			h2,
			h3 {
				margin: 0 0 1.2em;
			}
		}
		&__photos-holder,
		&__photos {
			display: contents;
		}
		&__photos > img {
			width: auto;
			height: 51.8rem;
			&:last-child {
				margin-right: 2rem;
			}
		}
	}

	@media (config.$lg-up) {
		&__items {
			transform: translateX(calc((var(--vw) - var(--content-width)) / 2));
		}
	}
}
