@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.f-method {
	$s: &;
	container-type: inline-size;
	&__helper {
		--gap-x: 0.8rem;
		--gap-y: 0.2rem;
		--padding-x-inp: calc(var(--padding-x) + var(--gap-x) + 2.4rem);
		--padding-x: 1.6rem;
		--padding-y: 1.4rem;
		--min-height: 5.4rem;
		margin: 0 0 3.2rem;
	}
	&__list {
		@extend %reset-ul;
		display: grid;
		grid-template-columns: auto 1fr auto auto;
	}
	&__item {
		@extend %reset-ul-li;
		position: relative;
		display: grid;
		grid-template-columns: subgrid;
		grid-column: auto / span 4;
		gap: var(--gap-y) var(--gap-x);
		min-height: var(--min-height);
		margin: 0 0 -0.1rem;
		padding: var(--padding-y) var(--padding-x);
		border: 0.1rem solid variables.$color-tile;
		border-radius: variables.$border-radius-md;
		background: variables.$color-white;
		transition: box-shadow variables.$t, border-color variables.$t;
		&:last-child {
			margin: 0;
		}
		&:has(input) {
			padding-left: var(--padding-x-inp);
		}
	}
	&__inp {
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		cursor: pointer;
		appearance: none;
	}
	&__label {
		display: contents;
	}
	&__inner,
	&__inner.inp-item__text {
		display: contents;
		font-size: 1.3rem;
		&::before,
		&::after {
			top: var(--padding-y);
			left: var(--padding-x);
		}
		&::after {
			top: calc(var(--padding-y) + 0.55rem);
			left: calc(var(--padding-x) + 0.55rem);
		}
	}
	&__img img {
		border-radius: variables.$border-radius-sm;
	}
	&__item:has(&__point) &__img {
		grid-row: auto / span 2;
	}
	&__content a,
	&__content button {
		// &__change {
		position: relative;
		z-index: 1;
	}
	&__title {
		display: flex;
		gap: 0.2rem 0.6rem;
		flex-wrap: wrap;
		align-items: center;
		font-size: 1.4rem;
	}
	&__tooltip {
		--tooltip-icon-size: 1.5rem;
	}
	&__flag {
		--flag-fs: 1.1rem;
		--flag-h: 2.2rem;
	}
	&__point {
		grid-column: 1 / span 3;
		margin-top: 1.2rem;
		padding-top: 1rem;
		border-top: 0.1rem solid variables.$color-tile-light;
	}
	&__desc {
		color: variables.$color-help;
	}
	&__price {
		text-align: right;
	}
	// &__change {
	// 	grid-column: 2;
	// 	font-size: 1.3rem;
	// 	&.item-icon {
	// 		--icon-size: 1.2rem;
	// 		--gap: 0.4rem;
	// 	}
	// }
	&__more {
		margin: 0.8rem 0 0;
		font-size: 1.3rem;
		.item-icon {
			--icon-size: 1.2rem;
			--gap: 0.4rem;
		}
	}

	// STATES
	&:not(:has(&__item.u-d-n)) &__more {
		display: none;
	}
	&__item:has(&__inp:checked) {
		z-index: 1;
		border-color: variables.$color-primary;
		#{$s}__delivery,
		#{$s}__price {
			font-weight: bold;
		}
	}

	// MODIF
	&--deliveryMethod &__img {
		width: 3rem;

		img {
			background: variables.$color-white-hover;
		}
	}
	&--paymentMethod {
		#{$s}__list {
			grid-template-columns: 1fr auto;
		}
		#{$s}__item {
			grid-column: auto/span 2;
		}
		#{$s}__img {
			display: flex;
			gap: 0.4rem;
			img {
				width: 2.5rem;
			}
		}
	}

	// HOVERS
	.hoverevents &__item:hover {
		z-index: 1;
		border-color: variables.$color-icon-minor;
	}

	// MQ
	@container (max-width: 629px) {
		&--deliveryMethod {
			#{$s}__content {
				grid-area: 1/2/1/5;
			}
			#{$s}__delivery {
				grid-area: 2/2/2/2;
			}
			#{$s}__price {
				grid-area: 2/3/2/3;
			}
			#{$s}__point {
				margin-left: calc(-2.4rem - var(--gap-x));
			}
		}
	}
	@container (min-width: 630px) {
		&__helper {
			--gap-x: 2rem;
			--gap-y: 0rem;
			--min-height: 8.6rem;
			--padding-x: 2.4rem;
			--padding-y: 2rem;
			margin: 0 0 3.6rem;
		}
		&__item {
			align-items: center;
		}
		&__inner,
		&__inner.inp-item__text {
			font-size: 1.5rem;
			&::before {
				top: calc(var(--min-height) / 2);
				transform: translateY(-50%);
			}
			&::after {
				top: calc(var(--min-height) / 2);
				transform: translateY(-50%);
			}
		}
		&__img {
			align-self: flex-start;
		}
		&__title {
			font-size: 1.5rem;
		}
		&__delivery {
			text-align: right;
		}
		&__point {
			grid-column: 2;
		}
		&__desc {
			display: flex;
			font-size: 1.4rem;
		}
		&__more {
			margin: 1.2rem 0 0;
			font-size: 1.4rem;
		}

		// MODIF
		&--deliveryMethod {
			#{$s}__img {
				width: 4.6rem;
				margin-right: -0.4rem;
			}
		}
		&--paymentMethod {
			#{$s}__helper {
				--min-height: 5.7rem;
			}
		}
	}
}
