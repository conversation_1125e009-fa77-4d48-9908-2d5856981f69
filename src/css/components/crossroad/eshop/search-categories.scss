@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.c-search-categories {
	padding: 2rem 2rem 2.5rem;
	font-size: 1.5rem;
	&__grid {
		--grid-x-spacing: 1.5rem;
		--grid-y-spacing: 1.3rem;
	}
	&__list {
		@extend %reset-ul;
		display: flex;
		gap: 1rem;
		flex-direction: column;
		margin: 0 0 1rem;
	}
	&__item {
		@extend %reset-ul-li;
		margin: 0;
	}
	&__more button {
		color: variables.$color-gray;
		span:last-child {
			display: none;
		}
	}

	// STATES
	&__cell:not(.is-open) &__item--hidden {
		display: none;
	}
	&__cell.is-open &__more button {
		span:first-child {
			display: none;
		}
		span:last-child {
			display: block;
		}
	}

	// MQ
	@media (config.$xs-up) {
		--column-width: calc(100% / 2);
		&__cell {
			width: var(--column-width);
		}
	}
	@media (config.$sm-up) {
		--column-width: calc(100% / 3);
		padding: 2.6rem 4rem 3.6rem;
	}
	@media (config.$xl-up) {
		--column-width: calc(100% / 5);
	}
}
