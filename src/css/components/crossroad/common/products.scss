@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.c-products {
	--cell-width: 100%;
	--line-offset: 1.6rem;
	&__item {
		width: var(--cell-width);
		&::before {
			content: '';
			position: absolute;
			top: var(--line-offset);
			bottom: var(--line-offset);
			left: 100%;
			width: 0.1rem;
			background: variables.$color-tile-light;
		}
		&::after {
			content: '';
			position: absolute;
			top: 100%;
			right: var(--line-offset);
			left: var(--line-offset);
			height: 0.1rem;
			background: variables.$color-tile-light;
		}
	}
	&__pager {
		padding-top: 2rem;
	}

	// MODIF
	&__item--edu::before,
	&__item--edu::after {
		content: none;
	}

	// MQ
	@media (max-width: 635px) {
		&__item::before {
			content: none;
		}
	}

	@media (min-width: 636px) {
		--cell-width: 50%;
		@media (config.$md-down) {
			&__item:nth-child(2n + 2)::before {
				content: none;
			}
			&__item--edu ~ &__item::before {
				content: '';
			}
			&__item--edu ~ &__item:nth-child(2n + 3)::before {
				content: none;
			}
		}
	}
	@media (config.$md-up) {
		--line-offset: 3.2rem;
		&__pager {
			padding-top: 3.2rem;
		}
		@media (config.$lg-down) {
			&__item:nth-child(3n + 3)::before {
				content: none;
			}
		}
	}
	@media (config.$lg-up) {
		--cell-width: 50%;
		@media (config.$xl-down) {
			&__item:nth-child(2n + 2)::before {
				content: none;
			}
			&__item--edu ~ &__item::before {
				content: '';
			}
			&__item--edu ~ &__item:nth-child(2n + 3)::before {
				content: none;
			}
		}
	}
	@media (config.$xl-up) {
		--cell-width: 33.33%;
		&__item:nth-child(3n + 3)::before {
			content: none;
		}
		&__item--edu ~ &__item::before {
			content: '';
		}
		&__item--edu ~ &__item:nth-child(3n + 4)::before {
			content: none;
		}
	}
}
