@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.m-currency {
	$s: &;
	position: relative;
	&__btn {
		display: flex;
	}
	&__box {
		position: absolute;
		right: 0;
		bottom: 100%;
		z-index: 1;
		display: flex;
		gap: 1rem;
		flex-direction: column;
		width: 12rem;
		margin: 0 0 0.5rem;
		padding: 2rem;
		border-radius: variables.$border-radius-sm;
		background: variables.$color-white;
		visibility: hidden;
		opacity: 0;
		transition: opacity variables.$t, visibility variables.$t;
		box-shadow: 0 0.5rem 2rem rgba(black, 0.15);
	}

	// MODIF
	.header--order &__box {
		top: 100%;
		bottom: auto;
		margin: 0.5rem 0 0;
	}

	// HOVERS
	&.is-open,
	.hoverevents &.is-hover,
	.no-js.hoverevents &:hover {
		#{$s}__btn .icon-svg {
			transform: scale(-1);
		}
		#{$s}__box {
			visibility: visible;
			opacity: 1;
		}
	}

	// MQ
	@media (config.$lg-up) {
		&__box {
			top: 100%;
			bottom: auto;
			margin: 0;
		}
	}
}
